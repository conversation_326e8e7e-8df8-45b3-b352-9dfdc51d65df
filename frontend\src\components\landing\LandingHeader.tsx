import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Link } from 'react-router-dom'
import { Menu, X, Zap } from 'lucide-react'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { cn } from '../../lib/utils'

export function LandingHeader() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { name: 'Fonctionnalités', href: '#features' },
    { name: 'Automatisations', href: '#automations' },
    { name: 'Tarifs', href: '#pricing' },
    { name: 'Documentation', href: '#docs' },
  ]

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled
          ? 'bg-white/95 backdrop-blur-xl border-b border-neutral-200/50 shadow-sm'
          : 'bg-transparent'
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Modern Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-r from-brand-500 to-brand-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-glow transition-all duration-300">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <Badge
                size="sm"
                className="absolute -top-2 -right-2 bg-success-500 text-white border-0 w-4 h-4 p-0 text-xs animate-pulse"
              >
                ●
              </Badge>
            </div>
            <span className={cn(
              "text-xl font-bold transition-colors duration-300",
              isScrolled
                ? "text-neutral-900"
                : "text-white"
            )}>
              AutoFlow
            </span>
          </Link>

          {/* Modern Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={cn(
                  "px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:scale-105",
                  isScrolled
                    ? "text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                )}
              >
                {item.name}
              </a>
            ))}
          </nav>

          {/* Modern Desktop CTA */}
          <div className="hidden md:flex items-center space-x-3">
            <Link to="/login">
              <Button
                variant="ghost"
                size="md"
                className={cn(
                  "transition-colors duration-200",
                  isScrolled
                    ? "text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                )}
              >
                Connexion
              </Button>
            </Link>
            <Link to="/signup">
              <Button
                variant="gradient"
                size="md"
                className="shadow-lg hover:shadow-glow"
              >
                Commencer gratuitement
              </Button>
            </Link>
          </div>

          {/* Modern Mobile menu button */}
          <Button
            variant="ghost"
            size="md"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={cn(
              "md:hidden",
              isScrolled
                ? "text-neutral-600 hover:text-neutral-900"
                : "text-white/80 hover:text-white"
            )}
            aria-label="Menu de navigation"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </Button>
        </div>
      </div>

      {/* Modern Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-white/95 backdrop-blur-xl border-b border-neutral-200/50 shadow-lg"
          >
            <div className="px-4 py-6 space-y-2">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-3 text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100 rounded-xl transition-all duration-200 font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <div className="pt-4 space-y-3">
                <Link to="/login" onClick={() => setIsMobileMenuOpen(false)}>
                  <Button
                    variant="ghost"
                    size="lg"
                    fullWidth
                    className="text-neutral-600 hover:text-neutral-900"
                  >
                    Connexion
                  </Button>
                </Link>
                <Link to="/signup" onClick={() => setIsMobileMenuOpen(false)}>
                  <Button
                    variant="gradient"
                    size="lg"
                    fullWidth
                    className="shadow-lg"
                  >
                    Commencer gratuitement
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  )
}
