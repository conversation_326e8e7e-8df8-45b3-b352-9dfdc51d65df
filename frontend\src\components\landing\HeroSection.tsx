import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { ArrowR<PERSON>, Play, Sparkles, Zap, Workflow, Star, Code, Database, Globe } from 'lucide-react'
import { useInView } from 'react-intersection-observer'
import { Button } from '../ui/Button'
import { cn } from '../../lib/utils'

export function HeroSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  const floatingIcons = [
    { icon: Zap, delay: 0, x: 100, y: 50 },
    { icon: Workflow, delay: 1, x: -80, y: 80 },
    { icon: Spark<PERSON>, delay: 2, x: 120, y: -60 },
  ]

  return (
    <section ref={ref} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-slate-950">
      {/* Ethereal Shadow Background - Old Money Future Style */}
      <div className="absolute inset-0">
        {/* Base dark gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-950 via-slate-900 to-black"></div>

        {/* Ethereal Shadow Effect */}
        <div className="absolute inset-0">
          {/* Main ethereal glow */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-400/20 via-yellow-500/15 to-amber-600/20 rounded-full blur-3xl animate-pulse opacity-60"></div>
          <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-slate-400/15 via-blue-400/10 to-slate-500/15 rounded-full blur-3xl animate-pulse opacity-50" style={{ animationDelay: '2s' }}></div>

          {/* Secondary ethereal layers */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-amber-300/8 via-transparent to-slate-400/8 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }}></div>

          {/* Subtle noise overlay */}
          <div className="absolute inset-0 opacity-10" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`
          }}></div>
        </div>
      </div>

      {/* Main Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
      >
        {/* StackVault Badge */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
            <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
            <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
              🔥 La boîte noire de puissance
            </span>
          </div>
        </motion.div>

        {/* StackVault Typography */}
        <motion.h1
          variants={itemVariants}
          className="text-5xl md:text-6xl lg:text-7xl font-black mb-8 tracking-tight leading-[0.9]"
        >
          <span className="block text-white mb-2">
            Tu n'as pas accès à des
          </span>
          <span className="block mb-2">
            <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
              automatisations
            </span>
          </span>
          <span className="block text-slate-300 text-3xl md:text-4xl lg:text-5xl font-medium">
            Tu as accès à ce que d'autres payent des freelances 2 000 €/mois pour créer
          </span>
        </motion.h1>

        {/* StackVault Subtitle */}
        <motion.p
          variants={itemVariants}
          className="text-lg text-slate-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
        >
          Des stacks d'automatisation prêts à déployer, créés par des experts,
          pour que tu puisses te concentrer sur ce qui compte vraiment : faire du business.
        </motion.p>

        {/* Elite CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16"
        >
          <Button className="bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-black px-10 py-4 rounded-lg font-semibold shadow-2xl shadow-amber-400/25 hover:shadow-amber-400/40 transition-all duration-300 border border-amber-300/50">
            Accéder aux Stacks
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>

          <Button
            variant="outline"
            className="bg-black/20 backdrop-blur-xl border border-slate-600/50 text-slate-300 hover:text-white hover:border-slate-500 px-10 py-4 rounded-lg font-medium transition-all duration-300"
          >
            Voir les Exemples
          </Button>
        </motion.div>

        {/* Elite Partner Ecosystem */}
        <motion.div
          variants={itemVariants}
          className="flex items-center justify-center gap-12 mb-16 opacity-70"
        >
          {[
            { name: "N8N", icon: "⚡" },
            { name: "Make", icon: "🔗" },
            { name: "Zapier", icon: "⚙️" },
            { name: "OpenAI", icon: "🧠" },
            { name: "Claude", icon: "💎" },
          ].map((tool, index) => (
            <div key={index} className="flex items-center gap-3 text-slate-400 text-sm font-medium hover:text-amber-400 transition-colors duration-300">
              <span className="text-lg filter grayscale hover:grayscale-0 transition-all duration-300">{tool.icon}</span>
              <span className="font-light tracking-wide">{tool.name}</span>
            </div>
          ))}
        </motion.div>

        {/* Elite Command Center - Old Money Future Style */}
        <motion.div
          variants={itemVariants}
          className="relative max-w-5xl mx-auto"
        >
          {/* Main central orb */}
          <div className="relative mx-auto w-80 h-80 md:w-96 md:h-96">
            {/* Outer ring with gold gradient */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-amber-400/80 via-amber-500/60 to-amber-600/80 p-1 shadow-2xl shadow-amber-400/30">
              <div className="w-full h-full rounded-full bg-gradient-to-br from-slate-900 via-black to-slate-950 flex items-center justify-center border border-amber-400/20">
                {/* Inner content */}
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-amber-400/20 to-amber-600/20 rounded-full flex items-center justify-center backdrop-blur-xl border border-amber-400/30">
                    <Sparkles className="w-10 h-10 text-amber-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-3 tracking-wide">Elite Orchestration</h3>
                  <p className="text-sm text-slate-400 font-light">Precision & Excellence</p>
                </div>
              </div>
            </div>
          </div>

          {/* Elite floating command cards */}
          <motion.div
            className="absolute top-8 left-8 bg-black/60 backdrop-blur-xl rounded-xl shadow-2xl p-5 border border-amber-400/20"
            animate={{ y: [0, -12, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <div className="flex items-center gap-3">
              <Code className="w-5 h-5 text-amber-400" />
              <span className="text-sm font-medium text-slate-200">Deploy enterprise workflow...</span>
            </div>
          </motion.div>

          <motion.div
            className="absolute top-16 right-8 bg-black/60 backdrop-blur-xl rounded-xl shadow-2xl p-5 border border-slate-600/30"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1.5 }}
          >
            <div className="flex items-center gap-3">
              <Database className="w-5 h-5 text-slate-400" />
              <span className="text-sm font-medium text-slate-200">Orchestrate data streams</span>
            </div>
          </motion.div>

          <motion.div
            className="absolute bottom-8 left-16 bg-black/60 backdrop-blur-xl rounded-xl shadow-2xl p-5 border border-amber-400/20"
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 3 }}
          >
            <div className="flex items-center gap-3">
              <Globe className="w-5 h-5 text-amber-400" />
              <span className="text-sm font-medium text-slate-200">Global automation</span>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

    </section>
  )
}
