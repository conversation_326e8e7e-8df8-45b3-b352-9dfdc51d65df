import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { <PERSON>R<PERSON>, Play, Sparkles, Zap, Workflow, Star } from 'lucide-react'
import { useInView } from 'react-intersection-observer'
import { But<PERSON> } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { cn } from '../../lib/utils'

export function HeroSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  const floatingIcons = [
    { icon: Zap, delay: 0, x: 100, y: 50 },
    { icon: Workflow, delay: 1, x: -80, y: 80 },
    { icon: Sparkles, delay: 2, x: 120, y: -60 },
  ]

  return (
    <section ref={ref} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 via-brand-50/30 to-neutral-50">
      {/* Background Effects */}
      <div className="absolute inset-0">
        {/* Modern Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-brand-500/10 to-brand-600/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-brand-400/10 to-brand-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-brand-300/5 to-brand-400/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23a855f7' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* Modern Floating Elements */}
      {floatingIcons.map((item, index) => (
        <motion.div
          key={index}
          className="absolute hidden lg:block"
          initial={{ opacity: 0, scale: 0 }}
          animate={inView ? {
            opacity: 0.8,
            scale: 1,
            x: [0, item.x, 0],
            y: [0, item.y, 0],
          } : {}}
          transition={{
            duration: 8,
            delay: item.delay,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut"
          }}
          style={{
            left: `${20 + index * 20}%`,
            top: `${30 + index * 15}%`,
          }}
        >
          <div className="w-20 h-20 bg-white/80 backdrop-blur-xl rounded-3xl border border-brand-200/50 shadow-lg flex items-center justify-center hover:shadow-glow transition-all duration-300">
            <item.icon className="w-10 h-10 text-brand-600" />
          </div>
        </motion.div>
      ))}

      {/* Main Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
      >
        {/* Modern Badge */}
        <motion.div variants={itemVariants} className="mb-8">
          <Badge
            variant="primary"
            size="lg"
            leftIcon={<Sparkles className="w-4 h-4" />}
            className="bg-brand-100/80 text-brand-800 border border-brand-200/50 backdrop-blur-xl shadow-sm"
          >
            ✨ Nouvelle génération d'automatisations
          </Badge>
        </motion.div>

        {/* Modern Headline */}
        <motion.h1
          variants={itemVariants}
          className="text-5xl md:text-7xl lg:text-8xl font-black mb-8 tracking-tight"
        >
          <span className="bg-gradient-to-r from-neutral-900 via-neutral-800 to-neutral-900 bg-clip-text text-transparent">
            Automatisez
          </span>
          <br />
          <span className="bg-gradient-to-r from-brand-600 via-brand-500 to-brand-600 bg-clip-text text-transparent">
            l'impossible
          </span>
        </motion.h1>

        {/* Modern Subtitle */}
        <motion.p
          variants={itemVariants}
          className="text-xl md:text-2xl text-neutral-600 mb-12 max-w-4xl mx-auto leading-relaxed font-medium"
        >
          Créez, déployez et gérez vos automatisations avec la plateforme la plus avancée du marché.
          Compatible avec <span className="text-brand-600 font-semibold">N8N</span>,
          <span className="text-brand-700 font-semibold"> Make</span>, et bien plus encore.
        </motion.p>

        {/* Modern CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16"
        >
          <Link to="/signup">
            <Button
              variant="gradient"
              size="lg"
              rightIcon={<ArrowRight className="w-5 h-5" />}
              className="shadow-glow hover:shadow-glow-lg"
            >
              Commencer gratuitement
            </Button>
          </Link>

          <Button
            variant="outline"
            size="lg"
            leftIcon={<Play className="w-5 h-5" />}
            className="bg-white/80 backdrop-blur-xl border-neutral-200 text-neutral-700 hover:bg-white hover:shadow-md"
          >
            Voir la démo
          </Button>
        </motion.div>

        {/* Modern Stats */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          {[
            { number: "10K+", label: "Automatisations créées", icon: "🚀" },
            { number: "99.9%", label: "Uptime garanti", icon: "⚡" },
            { number: "500+", label: "Intégrations disponibles", icon: "🔗" },
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center p-6 bg-white/60 backdrop-blur-xl rounded-2xl border border-neutral-200/50 shadow-sm hover:shadow-md transition-all duration-300"
              whileHover={{ y: -2 }}
            >
              <div className="text-2xl mb-2">{stat.icon}</div>
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-brand-600 to-brand-700 bg-clip-text text-transparent mb-2">
                {stat.number}
              </div>
              <div className="text-neutral-600 text-sm font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* Modern Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center bg-white/50 backdrop-blur-sm">
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-1 h-3 bg-brand-500 rounded-full mt-2"
          />
        </div>
      </motion.div>
    </section>
  )
}
