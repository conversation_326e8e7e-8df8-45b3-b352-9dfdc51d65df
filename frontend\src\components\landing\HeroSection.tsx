import { motion } from 'framer-motion'
import { <PERSON> } from 'react-router-dom'
import { ArrowR<PERSON>, Play, Sparkles, Zap, Workflow, Star } from 'lucide-react'
import { useInView } from 'react-intersection-observer'
import { But<PERSON> } from '../ui/Button'
import { Badge } from '../ui/Badge'
import { cn } from '../../lib/utils'

export function HeroSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  const floatingIcons = [
    { icon: Zap, delay: 0, x: 100, y: 50 },
    { icon: Workflow, delay: 1, x: -80, y: 80 },
    { icon: Sparkles, delay: 2, x: 120, y: -60 },
  ]

  return (
    <section ref={ref} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 via-brand-50/30 to-neutral-50">
      {/* Clean Background */}
      <div className="absolute inset-0">
        {/* Subtle gradient overlay */}
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-gradient-to-r from-blue-100/20 to-purple-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-emerald-100/15 to-blue-100/15 rounded-full blur-3xl"></div>
      </div>

      {/* Main Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
      >
        {/* Simple Badge */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="inline-flex items-center px-4 py-2 bg-neutral-100 text-neutral-700 rounded-full text-sm font-medium">
            ✨ Nouvelle génération d'automatisations
          </div>
        </motion.div>

        {/* Clean, Readable Headline */}
        <motion.h1
          variants={itemVariants}
          className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 tracking-tight leading-tight"
        >
          <span className="text-neutral-900">
            Automatisez votre
          </span>
          <br />
          <span className="text-neutral-900">
            workflow avec
          </span>
          <br />
          <span className="text-blue-600">
            l'intelligence
          </span>
        </motion.h1>

        {/* Clean, Simple Subtitle */}
        <motion.p
          variants={itemVariants}
          className="text-lg md:text-xl text-neutral-600 mb-12 max-w-3xl mx-auto leading-relaxed"
        >
          Créez des automatisations puissantes en quelques clics.
          Compatible avec N8N, Make et plus de 500 intégrations.
        </motion.p>

        {/* Clean CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16"
        >
          <Link to="/signup">
            <Button
              size="lg"
              className="bg-neutral-900 text-white hover:bg-neutral-800 px-8 py-4 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Commencer gratuitement
            </Button>
          </Link>

          <Button
            variant="outline"
            size="lg"
            className="bg-white border-2 border-neutral-200 text-neutral-700 hover:bg-neutral-50 px-8 py-4 rounded-xl font-medium"
          >
            Voir la démo
          </Button>
        </motion.div>

        {/* Clean Stats */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col md:flex-row items-center justify-center gap-12 max-w-2xl mx-auto"
        >
          {[
            { number: "10K+", label: "Automatisations" },
            { number: "99.9%", label: "Uptime" },
            { number: "500+", label: "Intégrations" },
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-neutral-900 mb-1">
                {stat.number}
              </div>
              <div className="text-neutral-600 text-sm">{stat.label}</div>
            </div>
          ))}
        </motion.div>
      </motion.div>

      {/* Modern Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center bg-white/50 backdrop-blur-sm">
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-1 h-3 bg-brand-500 rounded-full mt-2"
          />
        </div>
      </motion.div>
    </section>
  )
}
