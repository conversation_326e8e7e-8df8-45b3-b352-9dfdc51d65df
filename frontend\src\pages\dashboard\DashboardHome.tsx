import { motion } from 'framer-motion'
import { 
  Workflow, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Plus,
  ArrowRight,
  Zap,
  BarChart3,
  Users
} from 'lucide-react'
import { Link } from 'react-router-dom'

export function DashboardHome() {
  const stats = [
    {
      name: 'Automatisations actives',
      value: '24',
      change: '+12%',
      changeType: 'positive',
      icon: Workflow,
      color: 'from-purple-500 to-pink-500'
    },
    {
      name: 'Exécutions ce mois',
      value: '12,847',
      change: '+23%',
      changeType: 'positive',
      icon: TrendingUp,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Temps économisé',
      value: '156h',
      change: '+8%',
      changeType: 'positive',
      icon: Clock,
      color: 'from-green-500 to-emerald-500'
    },
    {
      name: 'Taux de succès',
      value: '98.5%',
      change: '+0.3%',
      changeType: 'positive',
      icon: CheckCircle,
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  const recentAutomations = [
    {
      id: 1,
      name: 'Traitement des leads',
      status: 'active',
      lastRun: '2 min',
      executions: 1247,
      success: 98.2
    },
    {
      id: 2,
      name: 'Sync e-commerce',
      status: 'active',
      lastRun: '5 min',
      executions: 856,
      success: 99.1
    },
    {
      id: 3,
      name: 'Onboarding client',
      status: 'paused',
      lastRun: '1h',
      executions: 234,
      success: 97.8
    },
    {
      id: 4,
      name: 'Backup automatique',
      status: 'error',
      lastRun: '3h',
      executions: 12,
      success: 85.4
    }
  ]

  const quickActions = [
    {
      title: 'Nouvelle automatisation',
      description: 'Créer un workflow depuis un template',
      icon: Plus,
      href: '/dashboard/automations/new',
      color: 'from-purple-500 to-pink-500'
    },
    {
      title: 'Parcourir la bibliothèque',
      description: 'Explorer les templates disponibles',
      icon: BarChart3,
      href: '/dashboard/library',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Voir les analytics',
      description: 'Analyser les performances',
      icon: TrendingUp,
      href: '/dashboard/analytics',
      color: 'from-green-500 to-emerald-500'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-slate-900 mb-2">
          Tableau de bord
        </h1>
        <p className="text-slate-600">
          Bienvenue dans votre espace AutoFlow. Voici un aperçu de vos automatisations.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div variants={itemVariants}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div
              key={stat.name}
              className="bg-white rounded-2xl p-6 border border-slate-200 hover:shadow-lg transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  stat.changeType === 'positive' 
                    ? 'text-green-700 bg-green-100' 
                    : 'text-red-700 bg-red-100'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div className="text-2xl font-bold text-slate-900 mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-slate-600">
                {stat.name}
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-slate-900 mb-4">Actions rapides</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.href}
              className="group bg-white rounded-2xl p-6 border border-slate-200 hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                <action.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-slate-900 mb-2">
                {action.title}
              </h3>
              <p className="text-slate-600 text-sm mb-4">
                {action.description}
              </p>
              <div className="flex items-center text-purple-600 text-sm font-medium group-hover:text-purple-700">
                Commencer
                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
              </div>
            </Link>
          ))}
        </div>
      </motion.div>

      {/* Recent Automations */}
      <motion.div variants={itemVariants}>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-slate-900">Automatisations récentes</h2>
          <Link
            to="/dashboard/automations"
            className="text-purple-600 hover:text-purple-700 text-sm font-medium flex items-center"
          >
            Voir tout
            <ArrowRight className="w-4 h-4 ml-1" />
          </Link>
        </div>
        
        <div className="bg-white rounded-2xl border border-slate-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50 border-b border-slate-200">
                <tr>
                  <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                    Nom
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                    Statut
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                    Dernière exécution
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                    Exécutions
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                    Succès
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-200">
                {recentAutomations.map((automation) => (
                  <tr key={automation.id} className="hover:bg-slate-50 transition-colors">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <Workflow className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-medium text-slate-900">
                          {automation.name}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        automation.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : automation.status === 'paused'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {automation.status === 'active' && <CheckCircle className="w-3 h-3 mr-1" />}
                        {automation.status === 'error' && <AlertTriangle className="w-3 h-3 mr-1" />}
                        {automation.status === 'paused' && <Clock className="w-3 h-3 mr-1" />}
                        {automation.status === 'active' ? 'Actif' : 
                         automation.status === 'paused' ? 'En pause' : 'Erreur'}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-sm text-slate-600">
                      Il y a {automation.lastRun}
                    </td>
                    <td className="py-4 px-6 text-sm text-slate-900 font-medium">
                      {automation.executions.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-sm text-slate-900 font-medium">
                      {automation.success}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
