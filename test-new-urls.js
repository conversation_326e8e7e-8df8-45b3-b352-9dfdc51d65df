// Test rapide des nouvelles URLs corrigées
const puppeteer = require('puppeteer');

// URLs corrigées à tester
const TEST_URLS = [
  {
    name: 'LeBonCoin Lyon',
    url: 'https://www.leboncoin.fr/recherche?category=9&text=entrepot&locations=r_11',
    usesPuppeteer: true
  },
  {
    name: 'LeBonCoin Bordeaux', 
    url: 'https://www.leboncoin.fr/recherche?category=9&text=entrepot&locations=r_2',
    usesPuppeteer: true
  },
  {
    name: '<PERSON><PERSON>oger <PERSON>',
    url: 'https://www.seloger.com/list.htm?types=11&places=Lyon',
    usesPuppeteer: true
  },
  {
    name: 'Geolocaux général',
    url: 'https://www.geolocaux.com/vente/entrepot/',
    usesPuppeteer: false
  }
];

// Fonction pour tester avec Puppeteer
async function testWithPuppeteer(url, name) {
  let browser;
  try {
    console.log(`🤖 Test Puppeteer: ${name}`);
    console.log(`🔗 URL: ${url}`);
    
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage'
      ]
    });

    const page = await browser.newPage();
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    const startTime = Date.now();
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
    const loadTime = Date.now() - startTime;
    
    // Attendre que le contenu se charge
    await page.waitForTimeout(2000);
    
    const html = await page.content();
    
    console.log(`✅ Page chargée en ${loadTime}ms`);
    console.log(`📄 Taille: ${html.length} caractères`);
    
    // Analyser le contenu
    const title = await page.title();
    console.log(`📝 Titre: ${title}`);
    
    // Chercher des indicateurs de succès
    if (html.includes('404') || html.includes('Page non trouvée')) {
      console.log(`❌ Page 404 détectée`);
      return false;
    }
    
    if (html.includes('robot') || html.includes('captcha') || html.includes('blocked')) {
      console.log(`🤖 Protection anti-bot détectée`);
      return false;
    }
    
    // Chercher des prix
    const priceMatches = html.match(/(\d+(?:\s?\d+)*)\s*€/g);
    if (priceMatches) {
      console.log(`💰 ${priceMatches.length} prix trouvés`);
      console.log(`💰 Exemples: ${priceMatches.slice(0, 3).join(', ')}`);
    }
    
    // Chercher des mots-clés d'annonces
    const propertyKeywords = ['annonce', 'vente', 'immobilier', 'entrepot', 'entrepôt'];
    const foundKeywords = propertyKeywords.filter(keyword => 
      html.toLowerCase().includes(keyword)
    );
    console.log(`🏷️ Mots-clés: ${foundKeywords.join(', ')}`);
    
    // Chercher des liens d'annonces
    const links = await page.$$eval('a[href]', links => 
      links.map(link => link.href).filter(href => 
        href.includes('annonce') || 
        href.includes('vente') || 
        href.includes('detail') ||
        href.includes('bien')
      )
    );
    
    console.log(`🔗 ${links.length} liens d'annonces trouvés`);
    if (links.length > 0) {
      console.log(`🔗 Exemples: ${links.slice(0, 2).join(', ')}`);
    }
    
    console.log(`✅ Test réussi pour ${name}\n`);
    return true;
    
  } catch (error) {
    console.log(`❌ Erreur: ${error.message}\n`);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Fonction pour tester avec HTTP simple
async function testWithHTTP(url, name) {
  try {
    console.log(`📡 Test HTTP: ${name}`);
    console.log(`🔗 URL: ${url}`);
    
    const https = require('https');
    const http = require('http');
    const client = url.startsWith('https') ? https : http;
    
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const req = client.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const loadTime = Date.now() - startTime;
          
          console.log(`✅ Réponse reçue en ${loadTime}ms`);
          console.log(`📄 Taille: ${data.length} caractères`);
          
          if (data.includes('404')) {
            console.log(`❌ Page 404 détectée`);
            resolve(false);
            return;
          }
          
          const priceMatches = data.match(/(\d+(?:\s?\d+)*)\s*€/g);
          if (priceMatches) {
            console.log(`💰 ${priceMatches.length} prix trouvés`);
          }
          
          console.log(`✅ Test réussi pour ${name}\n`);
          resolve(true);
        });
      });
      
      req.on('error', (error) => {
        console.log(`❌ Erreur: ${error.message}\n`);
        resolve(false);
      });
      
      req.setTimeout(10000, () => {
        req.destroy();
        console.log(`❌ Timeout\n`);
        resolve(false);
      });
    });
    
  } catch (error) {
    console.log(`❌ Erreur: ${error.message}\n`);
    return false;
  }
}

// Fonction principale de test
async function runTests() {
  console.log('🚀 === TEST DES NOUVELLES URLs ===\n');
  
  let successCount = 0;
  const totalTests = TEST_URLS.length;
  
  for (const testUrl of TEST_URLS) {
    let success = false;
    
    if (testUrl.usesPuppeteer) {
      success = await testWithPuppeteer(testUrl.url, testUrl.name);
    } else {
      success = await testWithHTTP(testUrl.url, testUrl.name);
    }
    
    if (success) {
      successCount++;
    }
    
    // Pause entre les tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('✅ === RÉSULTATS DES TESTS ===');
  console.log(`📊 ${successCount}/${totalTests} tests réussis`);
  console.log(`📈 Taux de réussite: ${Math.round((successCount / totalTests) * 100)}%`);
  
  if (successCount > 0) {
    console.log('\n💡 Les nouvelles URLs fonctionnent mieux !');
    console.log('🎯 Le scraper amélioré devrait maintenant trouver des annonces.');
  } else {
    console.log('\n⚠️ Problèmes persistants détectés.');
    console.log('💡 Considérez d\'autres approches ou sites alternatifs.');
  }
}

// Démarrer les tests
runTests().catch(console.error);
