import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  Download,
  Filter
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'

export function AnalyticsPage() {
  const executionData = [
    { name: 'Lun', executions: 120, success: 118, errors: 2 },
    { name: 'Mar', executions: 145, success: 142, errors: 3 },
    { name: 'Mer', executions: 167, success: 164, errors: 3 },
    { name: 'Jeu', executions: 134, success: 131, errors: 3 },
    { name: 'Ven', executions: 189, success: 185, errors: 4 },
    { name: 'Sam', executions: 98, success: 96, errors: 2 },
    { name: 'Dim', executions: 87, success: 85, errors: 2 }
  ]

  const automationPerformance = [
    { name: 'Lead Processing', executions: 1247, successRate: 98.2 },
    { name: 'E-commerce Sync', executions: 856, successRate: 99.1 },
    { name: 'Customer Onboarding', executions: 634, successRate: 97.8 },
    { name: 'Slack Notifications', executions: 567, successRate: 100 },
    { name: 'Data Backup', executions: 234, successRate: 85.4 }
  ]

  const categoryData = [
    { name: 'Marketing', value: 35, color: '#8B5CF6' },
    { name: 'Sales', value: 25, color: '#EC4899' },
    { name: 'E-commerce', value: 20, color: '#06B6D4' },
    { name: 'Productivity', value: 15, color: '#10B981' },
    { name: 'Other', value: 5, color: '#F59E0B' }
  ]

  const stats = [
    {
      name: 'Exécutions totales',
      value: '12,847',
      change: '+23%',
      changeType: 'positive',
      icon: Activity,
      color: 'from-purple-500 to-pink-500'
    },
    {
      name: 'Taux de succès moyen',
      value: '98.5%',
      change: '+0.3%',
      changeType: 'positive',
      icon: CheckCircle,
      color: 'from-green-500 to-emerald-500'
    },
    {
      name: 'Temps économisé',
      value: '156h',
      change: '+8%',
      changeType: 'positive',
      icon: Clock,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Erreurs ce mois',
      value: '17',
      change: '-12%',
      changeType: 'positive',
      icon: AlertTriangle,
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Analytics
          </h1>
          <p className="text-slate-600">
            Analysez les performances de vos automatisations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="flex items-center px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-xl transition-colors">
            <Filter className="w-5 h-5 mr-2" />
            Filtres
          </button>
          <button className="flex items-center px-4 py-2 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors">
            <Download className="w-5 h-5 mr-2" />
            Exporter
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl p-6 border border-slate-200"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <span className={`text-sm font-medium px-2 py-1 rounded-full flex items-center ${
                stat.changeType === 'positive' 
                  ? 'text-green-700 bg-green-100' 
                  : 'text-red-700 bg-red-100'
              }`}>
                {stat.changeType === 'positive' ? (
                  <TrendingUp className="w-3 h-3 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 mr-1" />
                )}
                {stat.change}
              </span>
            </div>
            <div className="text-2xl font-bold text-slate-900 mb-1">
              {stat.value}
            </div>
            <div className="text-sm text-slate-600">
              {stat.name}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Execution Trends */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-2xl border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-slate-900">
              Tendances d'exécution
            </h2>
            <div className="flex items-center space-x-2 text-sm">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-slate-600">Succès</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-slate-600">Erreurs</span>
              </div>
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={executionData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="name" stroke="#64748b" />
              <YAxis stroke="#64748b" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: '1px solid #e2e8f0',
                  borderRadius: '12px',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Line type="monotone" dataKey="success" stroke="#8B5CF6" strokeWidth={3} dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }} />
              <Line type="monotone" dataKey="errors" stroke="#EF4444" strokeWidth={3} dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Category Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl border border-slate-200 p-6"
        >
          <h2 className="text-xl font-semibold text-slate-900 mb-6">
            Répartition par catégorie
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={5}
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: '1px solid #e2e8f0',
                  borderRadius: '12px',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          <div className="grid grid-cols-2 gap-4 mt-4">
            {categoryData.map((category, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: category.color }}
                ></div>
                <span className="text-sm text-slate-600">{category.name}</span>
                <span className="text-sm font-medium text-slate-900">{category.value}%</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Performance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-2xl border border-slate-200 overflow-hidden"
      >
        <div className="p-6 border-b border-slate-200">
          <h2 className="text-xl font-semibold text-slate-900">
            Performance par automatisation
          </h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-50">
              <tr>
                <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                  Automatisation
                </th>
                <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                  Exécutions
                </th>
                <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                  Taux de succès
                </th>
                <th className="text-left py-4 px-6 text-sm font-medium text-slate-600">
                  Performance
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-200">
              {automationPerformance.map((automation, index) => (
                <tr key={index} className="hover:bg-slate-50 transition-colors">
                  <td className="py-4 px-6">
                    <span className="font-medium text-slate-900">
                      {automation.name}
                    </span>
                  </td>
                  <td className="py-4 px-6 text-slate-600">
                    {automation.executions.toLocaleString()}
                  </td>
                  <td className="py-4 px-6">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      automation.successRate >= 98 
                        ? 'bg-green-100 text-green-800'
                        : automation.successRate >= 95
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {automation.successRate}%
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${automation.successRate}%` }}
                      ></div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>
    </div>
  )
}
