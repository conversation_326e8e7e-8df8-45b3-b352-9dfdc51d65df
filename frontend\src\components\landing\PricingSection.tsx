import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Check, Zap, Crown, Rocket } from 'lucide-react'
import { Link } from 'react-router-dom'

export function PricingSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const plans = [
    {
      name: "Starter",
      price: "0",
      period: "Gratuit",
      description: "Parfait pour découvrir AutoFlow",
      icon: Zap,
      gradient: "from-blue-500 to-cyan-500",
      features: [
        "5 automatisations actives",
        "1,000 exécutions/mois",
        "Intégrations de base",
        "Support communautaire",
        "Templates prêts à l'emploi"
      ],
      cta: "Commencer gratuitement",
      popular: false
    },
    {
      name: "Pro",
      price: "49",
      period: "/mois",
      description: "Pour les équipes qui veulent aller plus loin",
      icon: Rocket,
      gradient: "from-purple-500 to-pink-500",
      features: [
        "Automatisations illimitées",
        "50,000 exécutions/mois",
        "Toutes les intégrations",
        "Support prioritaire",
        "Analytics avancées",
        "Workflows conditionnels",
        "API personnalisée"
      ],
      cta: "Essayer 14 jours gratuits",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Sur mesure",
      period: "",
      description: "Pour les grandes organisations",
      icon: Crown,
      gradient: "from-yellow-500 to-orange-500",
      features: [
        "Tout du plan Pro",
        "Exécutions illimitées",
        "SSO & SAML",
        "Support dédié 24/7",
        "SLA 99.99%",
        "Déploiement on-premise",
        "Formation personnalisée"
      ],
      cta: "Nous contacter",
      popular: false
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="pricing" ref={ref} className="py-32 relative">
      {/* Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <span className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full border border-purple-500/30 backdrop-blur-sm text-purple-200 text-sm font-medium">
              Tarifs
            </span>
          </motion.div>
          
          <motion.h2 
            variants={itemVariants}
            className="text-4xl md:text-6xl font-bold mb-6"
          >
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Choisissez votre
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              plan parfait
            </span>
          </motion.h2>
          
          <motion.p 
            variants={itemVariants}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Commencez gratuitement et évoluez selon vos besoins. 
            Tous les plans incluent notre garantie de satisfaction à 100%.
          </motion.p>
        </motion.div>

        {/* Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group ${plan.popular ? 'md:scale-110 md:z-10' : ''}`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                    Le plus populaire
                  </div>
                </div>
              )}

              <div className={`relative p-8 bg-slate-800/50 backdrop-blur-sm rounded-3xl border transition-all duration-300 hover:transform hover:scale-105 ${
                plan.popular 
                  ? 'border-purple-500/50 bg-gradient-to-b from-purple-500/10 to-pink-500/10' 
                  : 'border-slate-700/50 hover:border-slate-600/50'
              }`}>
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 bg-gradient-to-r ${plan.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <plan.icon className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-gray-400 text-sm mb-6">{plan.description}</p>
                  
                  <div className="mb-6">
                    {plan.price === "Sur mesure" ? (
                      <div className="text-3xl font-bold text-white">{plan.price}</div>
                    ) : (
                      <div className="flex items-baseline justify-center">
                        <span className="text-5xl font-bold text-white">{plan.price}€</span>
                        <span className="text-gray-400 ml-2">{plan.period}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center space-x-3">
                      <div className={`w-5 h-5 bg-gradient-to-r ${plan.gradient} rounded-full flex items-center justify-center flex-shrink-0`}>
                        <Check className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <Link
                  to={plan.name === "Enterprise" ? "/contact" : "/signup"}
                  className={`block w-full text-center py-4 px-6 rounded-2xl font-semibold transition-all duration-300 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transform hover:scale-105'
                      : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                  }`}
                >
                  {plan.cta}
                </Link>

                {/* Hover Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${plan.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Note */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-16"
        >
          <p className="text-gray-400 text-sm">
            Tous les plans incluent un essai gratuit de 14 jours • Aucune carte de crédit requise • 
            <span className="text-purple-400 hover:text-purple-300 cursor-pointer"> Voir la FAQ</span>
          </p>
        </motion.div>
      </div>
    </section>
  )
}
