import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { <PERSON>, Zap, Crown, Rocket, ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'

export function PricingSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const plans = [
    {
      name: "Initié",
      price: "99",
      period: "/mois",
      description: "Pour les indépendants & freelances prêts à se faire remplacer par l'IA",
      icon: Zap,
      gradient: "from-slate-400 to-slate-500",
      badge: "🟫",
      features: [
        "Jusqu'à 5 automatisations actives par mois",
        "Accès à 1 nouveau stack exclusif chaque semaine",
        "Mises à jour & support via Discord privé",
        "1 agent IA clonable / mois"
      ],
      value: "Valeur perçue : ~300 €/mois",
      margin: "Marge brute : > 95%",
      cta: "Devenir Initié",
      popular: false
    },
    {
      name: "Système",
      price: "199",
      period: "/mois",
      description: "Pour les petites équipes qui veulent dominer leurs ops",
      icon: Rocket,
      gradient: "from-amber-400 to-amber-500",
      badge: "🟧",
      features: [
        "Jusqu'à 15 automatisations mensuelles",
        "Accès anticipé aux stacks + priorité sur les requêtes",
        "3 agents IA par mois",
        "Formation \"Stack comme un pro\" offerte (valeur : 399 €)"
      ],
      value: "Valeur perçue : ~900 €/mois",
      margin: "Marge brute : > 95%",
      cta: "Dominer le Système",
      popular: true
    },
    {
      name: "Empire",
      price: "399",
      period: "/mois",
      description: "Tu veux des résultats ou tu veux jouer ? (10 places)",
      icon: Crown,
      gradient: "from-amber-500 to-amber-600",
      badge: "🟪",
      features: [
        "Accès illimité",
        "Requêtes de stacks à la demande (créées sous 5j ouvrés)",
        "Audit mensuel de tes process + suggestion automatisée",
        "5 agents IA/month, prêts à déployer"
      ],
      value: "Valeur perçue : 2 000–3 000 €/mois",
      margin: "Marge brute : > 97%",
      cta: "Rejoindre l'Empire",
      popular: false
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="investment" ref={ref} className="py-32 relative bg-gradient-to-b from-black via-slate-950 to-slate-900">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-400/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-slate-400/6 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-amber-300/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* StackVault Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-8">
            <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
                💥 PLAN D'ABONNEMENT (0 bullshit)
              </span>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              StackVault = La boîte noire de puissance
            </h3>
            <p className="text-lg text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Tu n'as pas accès à des automatisations.<br />
              <span className="text-amber-400 font-semibold">
                Tu as accès à ce que d'autres payent des freelances 2 000 €/mois pour créer.
              </span>
            </p>
          </motion.div>
        </motion.div>

        {/* Clean Premium Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group ${plan.popular ? 'lg:scale-105' : ''}`}
            >
              {/* Clean Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-black px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                    Plus populaire
                  </div>
                </div>
              )}

              <div className={`relative h-full rounded-2xl p-8 transition-all duration-300 hover:transform hover:scale-[1.02] ${
                plan.popular
                  ? 'bg-gradient-to-b from-slate-900 to-black border-2 border-amber-400/50 shadow-xl shadow-amber-400/10'
                  : 'bg-gradient-to-b from-slate-800 to-slate-900 border border-slate-600/50 hover:border-slate-500/70'
              }`}>
                {/* Clean Plan Header */}
                <div className="text-center mb-8">
                  {/* Plan Icon */}
                  <div className="flex justify-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-br ${plan.gradient} rounded-xl flex items-center justify-center shadow-lg`}>
                      <plan.icon className="w-8 h-8 text-black" />
                    </div>
                  </div>

                  {/* Plan Name */}
                  <h3 className={`text-2xl font-bold mb-2 ${
                    plan.popular ? 'text-amber-400' : 'text-white'
                  }`}>
                    Plan {plan.name}
                  </h3>

                  {/* Price */}
                  <div className="mb-4">
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-white">{plan.price}€</span>
                      <span className="text-slate-400 ml-2">{plan.period}</span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-400 text-sm leading-relaxed mb-8">
                    {plan.description}
                  </p>
                </div>

                {/* Clean Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start space-x-3">
                      <div className={`w-5 h-5 bg-gradient-to-r ${plan.gradient} rounded-full flex items-center justify-center flex-shrink-0 mt-0.5`}>
                        <Check className="w-3 h-3 text-black" />
                      </div>
                      <span className="text-slate-300 text-sm leading-relaxed">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Clean Value Metrics */}
                <div className="mb-8">
                  <div className={`p-4 rounded-xl border ${
                    plan.popular
                      ? 'bg-amber-400/5 border-amber-400/20'
                      : 'bg-slate-800/30 border-slate-600/30'
                  }`}>
                    <div className="space-y-2 text-center">
                      <div className={`text-sm font-medium ${plan.popular ? 'text-amber-400' : 'text-slate-400'}`}>
                        {plan.value}
                      </div>
                      <div className={`text-sm font-medium ${plan.popular ? 'text-amber-400' : 'text-slate-400'}`}>
                        {plan.margin}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Clean CTA Button */}
                <div className="mt-auto">
                  <Link
                    to={plan.name === "Empire" ? "/contact" : "/signup"}
                    className={`block w-full py-4 px-6 rounded-xl text-center font-semibold transition-all duration-300 hover:transform hover:scale-105 ${
                      plan.popular
                        ? 'bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-black shadow-lg shadow-amber-400/25'
                        : 'bg-white/10 text-white border border-slate-600/50 hover:bg-white/20 hover:border-slate-500/50'
                    }`}
                  >
                    {plan.cta}
                  </Link>

                  {/* Empire plan note */}
                  {plan.name === "Empire" && (
                    <div className="mt-3 text-center">
                      <span className="text-amber-400 text-xs font-medium">Seulement 10 places disponibles</span>
                    </div>
                  )}
                </div>

              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Elite Bottom Note */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-16"
        >
          <p className="text-slate-400 text-sm font-light">
            All tiers include a 14-day complimentary trial • No financial commitment required •
            <span className="text-amber-400 hover:text-amber-300 cursor-pointer font-medium"> Consult our Concierge</span>
          </p>
        </motion.div>
      </div>
    </section>
  )
}
