import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { <PERSON>, Zap, Crown, Rocket, ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'

export function PricingSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const plans = [
    {
      name: "Initié",
      price: "99",
      period: "/mois",
      description: "Pour les indépendants & freelances prêts à se faire remplacer par l'IA",
      icon: Zap,
      gradient: "from-slate-400 to-slate-500",
      badge: "🟫",
      features: [
        "Jusqu'à 5 automatisations actives par mois",
        "Accès à 1 nouveau stack exclusif chaque semaine",
        "Mises à jour & support via Discord privé",
        "1 agent IA clonable / mois"
      ],
      value: "Valeur perçue : ~300 €/mois",
      margin: "Marge brute : > 95%",
      cta: "Devenir Initié",
      popular: false
    },
    {
      name: "Système",
      price: "199",
      period: "/mois",
      description: "Pour les petites équipes qui veulent dominer leurs ops",
      icon: Rocket,
      gradient: "from-amber-400 to-amber-500",
      badge: "🟧",
      features: [
        "Jusqu'à 15 automatisations mensuelles",
        "Accès anticipé aux stacks + priorité sur les requêtes",
        "3 agents IA par mois",
        "Formation \"Stack comme un pro\" offerte (valeur : 399 €)"
      ],
      value: "Valeur perçue : ~900 €/mois",
      margin: "Marge brute : > 95%",
      cta: "Dominer le Système",
      popular: true
    },
    {
      name: "Empire",
      price: "399",
      period: "/mois",
      description: "Tu veux des résultats ou tu veux jouer ? (10 places)",
      icon: Crown,
      gradient: "from-amber-500 to-amber-600",
      badge: "🟪",
      features: [
        "Accès illimité",
        "Requêtes de stacks à la demande (créées sous 5j ouvrés)",
        "Audit mensuel de tes process + suggestion automatisée",
        "5 agents IA/month, prêts à déployer"
      ],
      value: "Valeur perçue : 2 000–3 000 €/mois",
      margin: "Marge brute : > 97%",
      cta: "Rejoindre l'Empire",
      popular: false
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="investment" ref={ref} className="py-32 relative bg-gradient-to-b from-black via-slate-950 to-slate-900">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-400/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-slate-400/6 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-amber-300/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* StackVault Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-8">
            <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
                💥 PLAN D'ABONNEMENT (0 bullshit)
              </span>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              StackVault = La boîte noire de puissance
            </h3>
            <p className="text-lg text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Tu n'as pas accès à des automatisations.<br />
              <span className="text-amber-400 font-semibold">
                Tu as accès à ce que d'autres payent des freelances 2 000 €/mois pour créer.
              </span>
            </p>
          </motion.div>
        </motion.div>

        {/* Premium Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group ${plan.popular ? 'lg:scale-110 lg:z-10' : ''}`}
            >
              {/* Premium Badge */}
              {plan.popular && (
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="relative">
                    <div className="bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 text-black px-8 py-3 rounded-2xl text-sm font-black shadow-2xl shadow-amber-400/40 border border-amber-300">
                      <div className="flex items-center space-x-2">
                        <span>🔥</span>
                        <span>PLUS POPULAIRE</span>
                        <span>🔥</span>
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-amber-600 rounded-2xl blur-lg opacity-50 -z-10"></div>
                  </div>
                </div>
              )}

              <div className={`relative overflow-hidden rounded-3xl transition-all duration-700 hover:transform hover:scale-105 group-hover:shadow-2xl ${
                plan.popular
                  ? 'bg-gradient-to-br from-amber-400/20 via-black to-amber-500/20 border-2 border-amber-400/60 shadow-2xl shadow-amber-400/25'
                  : 'bg-gradient-to-br from-slate-800/50 via-black to-slate-900/50 border border-slate-600/30 hover:border-slate-500/50'
              }`}>

                {/* Premium Background Effects */}
                <div className="absolute inset-0">
                  <div className={`absolute top-0 right-0 w-32 h-32 rounded-full blur-3xl ${
                    plan.popular ? 'bg-amber-400/20' : 'bg-slate-400/10'
                  }`}></div>
                  <div className={`absolute bottom-0 left-0 w-24 h-24 rounded-full blur-2xl ${
                    plan.popular ? 'bg-amber-500/15' : 'bg-slate-500/8'
                  }`}></div>
                </div>
                {/* Premium Plan Header */}
                <div className="relative z-10 p-8">
                  {/* Plan Badge & Icon */}
                  <div className="flex items-center justify-center mb-6">
                    <div className="relative">
                      <span className="text-4xl mr-4 filter drop-shadow-lg">{plan.badge}</span>
                      <div className={`w-16 h-16 bg-gradient-to-br ${plan.gradient} rounded-2xl flex items-center justify-center shadow-2xl border-2 ${
                        plan.popular ? 'border-amber-300/50' : 'border-white/20'
                      }`}>
                        <plan.icon className="w-8 h-8 text-black drop-shadow-sm" />
                      </div>
                      {/* Glow effect */}
                      <div className={`absolute inset-0 w-16 h-16 bg-gradient-to-br ${plan.gradient} rounded-2xl blur-xl opacity-50 -z-10`}></div>
                    </div>
                  </div>

                  {/* Plan Name */}
                  <div className="text-center mb-4">
                    <h3 className={`text-3xl font-black mb-2 tracking-tight ${
                      plan.popular
                        ? 'bg-gradient-to-r from-amber-200 via-amber-300 to-amber-400 bg-clip-text text-transparent'
                        : 'text-white'
                    }`}>
                      Plan {plan.name}
                    </h3>
                  </div>

                  {/* Price Display */}
                  <div className="text-center mb-6">
                    <div className="relative">
                      <div className="flex items-baseline justify-center mb-2">
                        <span className={`text-6xl font-black tracking-tight ${
                          plan.popular ? 'text-amber-400' : 'text-white'
                        }`}>
                          {plan.price}€
                        </span>
                        <span className="text-slate-400 ml-3 text-lg font-medium">{plan.period}</span>
                      </div>
                      {/* Price underline */}
                      <div className={`w-24 h-1 mx-auto rounded-full ${
                        plan.popular ? 'bg-gradient-to-r from-amber-400 to-amber-500' : 'bg-slate-600'
                      }`}></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-slate-300 text-center mb-8 font-light leading-relaxed px-2">
                    {plan.description}
                  </p>
                </div>

                {/* Premium Features */}
                <div className="relative z-10 px-8 space-y-5 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      className="flex items-start space-x-4 group"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: featureIndex * 0.1 }}
                    >
                      <div className={`relative w-6 h-6 bg-gradient-to-br ${plan.gradient} rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg mt-0.5 group-hover:scale-110 transition-transform duration-300`}>
                        <Check className="w-4 h-4 text-black font-bold" />
                        <div className={`absolute inset-0 bg-gradient-to-br ${plan.gradient} rounded-lg blur-md opacity-50 -z-10`}></div>
                      </div>
                      <span className="text-slate-200 font-medium leading-relaxed group-hover:text-white transition-colors duration-300">
                        {feature}
                      </span>
                    </motion.div>
                  ))}
                </div>

                {/* Premium Value Metrics */}
                <div className="relative z-10 mx-8 mb-8">
                  <div className={`p-6 rounded-2xl border-2 ${
                    plan.popular
                      ? 'bg-gradient-to-br from-amber-400/10 to-amber-500/5 border-amber-400/30'
                      : 'bg-gradient-to-br from-slate-800/30 to-slate-900/30 border-slate-600/30'
                  } backdrop-blur-xl`}>
                    <div className="space-y-3">
                      <div className={`flex items-center space-x-2 ${plan.popular ? 'text-amber-300' : 'text-slate-300'}`}>
                        <span className="text-lg">💰</span>
                        <span className="font-bold text-sm">{plan.value}</span>
                      </div>
                      <div className={`flex items-center space-x-2 ${plan.popular ? 'text-amber-300' : 'text-slate-300'}`}>
                        <span className="text-lg">📈</span>
                        <span className="font-bold text-sm">{plan.margin}</span>
                      </div>
                      {plan.name === "Empire" && (
                        <div className="flex items-center space-x-2 text-amber-300">
                          <span className="text-lg">🎯</span>
                          <span className="font-bold text-sm">Tu peux closer du B2B, startup, agence à ce prix sans honte</span>
                        </div>
                      )}
                    </div>

                    {/* Metrics highlight */}
                    <div className={`mt-4 pt-4 border-t ${
                      plan.popular ? 'border-amber-400/20' : 'border-slate-600/20'
                    }`}>
                      <div className="text-center">
                        <span className={`text-xs font-medium ${
                          plan.popular ? 'text-amber-400' : 'text-slate-400'
                        }`}>
                          ROI GARANTI
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Premium CTA Button */}
                <div className="relative z-10 px-8 pb-8">
                  <Link
                    to={plan.name === "Empire" ? "/contact" : "/signup"}
                    className={`group relative block w-full py-5 px-6 rounded-2xl text-center font-black text-lg transition-all duration-500 transform hover:scale-105 overflow-hidden ${
                      plan.popular
                        ? 'bg-gradient-to-r from-amber-400 via-amber-500 to-amber-600 hover:from-amber-500 hover:via-amber-600 hover:to-amber-700 text-black shadow-2xl shadow-amber-400/40 hover:shadow-amber-400/60'
                        : 'bg-gradient-to-r from-slate-700 via-slate-800 to-slate-900 hover:from-slate-600 hover:via-slate-700 hover:to-slate-800 text-white border-2 border-slate-600/50 hover:border-slate-500/70'
                    }`}
                  >
                    {/* Button background effect */}
                    <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${
                      plan.popular
                        ? 'bg-gradient-to-r from-amber-300 to-amber-400'
                        : 'bg-gradient-to-r from-slate-600 to-slate-700'
                    }`}></div>

                    {/* Button content */}
                    <span className="relative z-10 flex items-center justify-center space-x-2">
                      <span>{plan.cta}</span>
                      <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                    </span>

                    {/* Shine effect */}
                    <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12"></div>
                  </Link>

                  {/* Additional CTA info for Empire plan */}
                  {plan.name === "Empire" && (
                    <div className="mt-3 text-center">
                      <span className="text-amber-400 text-xs font-bold">⚡ SEULEMENT 10 PLACES DISPONIBLES</span>
                    </div>
                  )}
                </div>

                {/* Elite Hover Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${plan.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Elite Bottom Note */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-16"
        >
          <p className="text-slate-400 text-sm font-light">
            All tiers include a 14-day complimentary trial • No financial commitment required •
            <span className="text-amber-400 hover:text-amber-300 cursor-pointer font-medium"> Consult our Concierge</span>
          </p>
        </motion.div>
      </div>
    </section>
  )
}
