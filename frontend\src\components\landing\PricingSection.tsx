import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { <PERSON>, Zap, Crown, Rocket } from 'lucide-react'
import { Link } from 'react-router-dom'

export function PricingSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const plans = [
    {
      name: "Explorer",
      price: "0",
      period: "Complimentary",
      description: "Perfect for discovering digital excellence",
      icon: Zap,
      gradient: "from-slate-400 to-slate-500",
      features: [
        "5 elite automations",
        "1,000 monthly executions",
        "Essential integrations",
        "Community access",
        "Curated templates"
      ],
      cta: "Begin Journey",
      popular: false
    },
    {
      name: "Elite",
      price: "149",
      period: "/month",
      description: "For teams pursuing digital supremacy",
      icon: Rocket,
      gradient: "from-amber-400 to-amber-500",
      features: [
        "Unlimited automations",
        "100,000 monthly executions",
        "All premium integrations",
        "Priority concierge support",
        "Advanced intelligence analytics",
        "Conditional workflows",
        "Custom API access",
        "White-glove onboarding"
      ],
      cta: "Claim Elite Status",
      popular: true
    },
    {
      name: "Imperium",
      price: "Bespoke",
      period: "",
      description: "For digital empires and conglomerates",
      icon: Crown,
      gradient: "from-amber-500 to-amber-600",
      features: [
        "Everything in Elite",
        "Unlimited executions",
        "Enterprise SSO & SAML",
        "Dedicated success manager",
        "99.99% SLA guarantee",
        "Private cloud deployment",
        "Executive training program",
        "Custom development"
      ],
      cta: "Request Audience",
      popular: false
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="investment" ref={ref} className="py-32 relative bg-gradient-to-b from-black via-slate-950 to-slate-900">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-400/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-slate-400/6 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-amber-300/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Elite Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
                Investment Tiers
              </span>
            </div>
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-6xl font-black mb-6 tracking-tight"
          >
            <span className="block text-white mb-2">
              Choose Your
            </span>
            <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
              Digital Destiny
            </span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-400 max-w-3xl mx-auto font-light leading-relaxed"
          >
            Begin your ascension with complimentary access and scale to unprecedented heights.
            All tiers include our platinum guarantee of absolute satisfaction.
          </motion.p>
        </motion.div>

        {/* Pricing Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group ${plan.popular ? 'md:scale-110 md:z-10' : ''}`}
            >
              {/* Elite Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="bg-gradient-to-r from-amber-400 to-amber-500 text-black px-6 py-2 rounded-full text-sm font-bold shadow-lg shadow-amber-400/30">
                    Most Elite
                  </div>
                </div>
              )}

              <div className={`relative p-8 bg-black/60 backdrop-blur-xl rounded-3xl border transition-all duration-500 hover:transform hover:scale-105 ${
                plan.popular
                  ? 'border-amber-400/50 bg-gradient-to-b from-amber-400/10 to-amber-500/10 shadow-2xl shadow-amber-400/20'
                  : 'border-slate-700/50 hover:border-slate-600/50'
              }`}>
                {/* Elite Plan Header */}
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 bg-gradient-to-r ${plan.gradient} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                    <plan.icon className="w-8 h-8 text-black" />
                  </div>

                  <h3 className="text-2xl font-bold text-white mb-2 tracking-wide">{plan.name}</h3>
                  <p className="text-slate-400 text-sm mb-6 font-light">{plan.description}</p>

                  <div className="mb-6">
                    {plan.price === "Bespoke" ? (
                      <div className="text-3xl font-bold bg-gradient-to-r from-amber-300 to-amber-400 bg-clip-text text-transparent">{plan.price}</div>
                    ) : (
                      <div className="flex items-baseline justify-center">
                        <span className="text-5xl font-bold text-white">${plan.price}</span>
                        <span className="text-slate-400 ml-2 font-light">{plan.period}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Elite Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center space-x-3">
                      <div className={`w-5 h-5 bg-gradient-to-r ${plan.gradient} rounded-full flex items-center justify-center flex-shrink-0 shadow-sm`}>
                        <Check className="w-3 h-3 text-black" />
                      </div>
                      <span className="text-slate-300 text-sm font-light">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Elite CTA Button */}
                <Link
                  to={plan.name === "Imperium" ? "/contact" : "/signup"}
                  className={`block w-full text-center py-4 px-6 rounded-2xl font-semibold transition-all duration-300 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-black shadow-lg shadow-amber-400/25 transform hover:scale-105'
                      : 'bg-white/10 text-white border border-slate-600/50 hover:bg-white/20 hover:border-slate-500/50'
                  }`}
                >
                  {plan.cta}
                </Link>

                {/* Elite Hover Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${plan.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Elite Bottom Note */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-16"
        >
          <p className="text-slate-400 text-sm font-light">
            All tiers include a 14-day complimentary trial • No financial commitment required •
            <span className="text-amber-400 hover:text-amber-300 cursor-pointer font-medium"> Consult our Concierge</span>
          </p>
        </motion.div>
      </div>
    </section>
  )
}
