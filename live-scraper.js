// SCRAPER LIVE - Vraies annonces en temps réel avec vérification de disponibilité
const http = require('http');
const puppeteer = require('puppeteer');

const PORT = 3000;

// Critères de recherche selon ton cahier des charges
const SEARCH_CRITERIA = {
  minPrice: 800000,
  maxPrice: 900000,
  minBuildingArea: 400,
  idealBuildingArea: 1100,
  minLandArea: 3500,
  cities: ['Lyon', 'Bordeaux'],
  maxDistanceToHighway: 15,
  minCeilingHeight: 5.5,
  idealCeilingHeight: 6.5
};

// Base de données en mémoire pour stocker les annonces scrapées
let liveProperties = [];
let scrapingStatus = { isRunning: false, currentStep: '', progress: 0 };

// Fonction pour extraire le prix depuis le texte
function extractPrice(text) {
  if (!text) return 0;
  // Chercher différents formats de prix
  const patterns = [
    /(\d+(?:\s?\d+)*)\s*€/,
    /(\d+(?:\s?\d+)*)\s*euros?/i,
    /prix\s*:?\s*(\d+(?:\s?\d+)*)/i
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      return parseInt(match[1].replace(/\s/g, ''));
    }
  }
  return 0;
}

// Fonction pour extraire la superficie
function extractArea(text) {
  if (!text) return 0;
  const match = text.match(/(\d+(?:[,.]?\d+)?)\s*m[²2]/i);
  if (match) {
    return parseFloat(match[1].replace(',', '.'));
  }
  return 0;
}

// Fonction pour vérifier si une annonce est disponible
async function checkAvailability(page, url) {
  try {
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });

    // Vérifier les indicateurs de vente/indisponibilité
    const unavailableIndicators = await page.evaluate(() => {
      const text = document.body.textContent.toLowerCase();
      const unavailableKeywords = [
        'vendu', 'sold', 'retiré', 'indisponible', 'plus disponible',
        'hors marché', 'suspendu', 'réservé', 'en cours de vente'
      ];

      return unavailableKeywords.some(keyword => text.includes(keyword));
    });

    // Vérifier si la page existe encore (pas 404)
    const pageExists = await page.evaluate(() => {
      return !document.body.textContent.toLowerCase().includes('page non trouvée') &&
             !document.body.textContent.toLowerCase().includes('erreur 404');
    });

    return pageExists && !unavailableIndicators;
  } catch (error) {
    console.log(`⚠️ Erreur vérification ${url}: ${error.message}`);
    return false; // En cas d'erreur, considérer comme indisponible
  }
}

// Scraper pour SeLoger Bureaux & Commerces
async function scrapeSeLoger(browser) {
  console.log('🔍 Scraping SeLoger Bureaux & Commerces...');
  scrapingStatus.currentStep = 'Connexion à SeLoger...';

  const page = await browser.newPage();
  const properties = [];

  try {
    // URLs de recherche pour Lyon et Bordeaux
    const searchUrls = [
      'https://www.seloger-bureaux-commerces.com/achat/local-d-activites-entrepot/rhone-alpes/rhone/lyon-69000',
      'https://www.seloger-bureaux-commerces.com/achat/local-d-activites-entrepot/aquitaine/gironde/bordeaux-33000'
    ];

    for (const searchUrl of searchUrls) {
      try {
        scrapingStatus.currentStep = `Scraping ${searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux'} sur SeLoger...`;

        await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: 30000 });

        // Attendre que les annonces se chargent
        await page.waitForSelector('article, .property-item, .annonce', { timeout: 10000 });

        // Extraire les liens des annonces
        const propertyLinks = await page.evaluate(() => {
          const links = [];
          const selectors = [
            'article a[href*="/annonces/"]',
            '.property-item a[href*="/annonces/"]',
            'a[href*="/annonces/achat/"]'
          ];

          for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              const href = el.href;
              if (href && href.includes('/annonces/') && !links.includes(href)) {
                links.push(href);
              }
            });
            if (links.length > 0) break;
          }

          return links.slice(0, 10); // Limiter à 10 annonces par ville
        });

        console.log(`📋 ${propertyLinks.length} annonces trouvées sur SeLoger`);

        // Scraper chaque annonce individuellement
        for (let i = 0; i < propertyLinks.length; i++) {
          const link = propertyLinks[i];
          scrapingStatus.currentStep = `Vérification annonce ${i + 1}/${propertyLinks.length}...`;
          scrapingStatus.progress = (i / propertyLinks.length) * 50; // 50% pour SeLoger

          try {
            // Vérifier d'abord la disponibilité
            const isAvailable = await checkAvailability(page, link);
            if (!isAvailable) {
              console.log(`❌ Annonce indisponible: ${link}`);
              continue;
            }

            // Extraire les données de l'annonce
            const propertyData = await page.evaluate(() => {
              const getText = (selector) => {
                const el = document.querySelector(selector);
                return el ? el.textContent.trim() : '';
              };

              return {
                title: getText('h1, .property-title, .annonce-title') || 'Titre non trouvé',
                description: getText('.description, .property-description, .detail-description') || '',
                priceText: getText('.price, .property-price, .detail-price, .prix'),
                address: getText('.address, .property-address, .localisation, .adresse') || '',
                surfaceText: getText('.surface, .superficie, .detail-surface'),
                terrainText: getText('.terrain, .foncier, .land-area')
              };
            });

            // Traiter les données extraites
            const price = extractPrice(propertyData.priceText);
            const buildingArea = extractArea(propertyData.surfaceText);
            const landArea = extractArea(propertyData.terrainText);

            // Vérifier si l'annonce correspond aux critères
            if (price >= SEARCH_CRITERIA.minPrice && price <= SEARCH_CRITERIA.maxPrice &&
                buildingArea >= SEARCH_CRITERIA.minBuildingArea) {

              const property = {
                id: `seloger-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: propertyData.title,
                description: propertyData.description,
                price: price,
                city: link.includes('lyon') ? 'Lyon' : 'Bordeaux',
                address: propertyData.address,
                buildingArea: buildingArea,
                landArea: landArea,
                source: 'seloger',
                sourceUrl: link,
                publishedAt: new Date(), // À améliorer avec vraie date
                scrapedAt: new Date(),
                isNew: true,
                isAvailable: true,
                matchScore: calculateMatchScore({ price, buildingArea, landArea, city: link.includes('lyon') ? 'Lyon' : 'Bordeaux' })
              };

              properties.push(property);
              console.log(`✅ Annonce ajoutée: ${property.title} - ${property.price}€`);
            }

          } catch (error) {
            console.log(`❌ Erreur scraping ${link}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        console.log(`❌ Erreur scraping SeLoger: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ Erreur générale SeLoger: ${error.message}`);
  } finally {
    await page.close();
  }

  return properties;
}

// Scraper pour Geolocaux
async function scrapeGeolocaux(browser) {
  console.log('🔍 Scraping Geolocaux...');
  scrapingStatus.currentStep = 'Connexion à Geolocaux...';

  const page = await browser.newPage();
  const properties = [];

  try {
    // URLs de recherche pour Lyon et Bordeaux
    const searchUrls = [
      'https://www.geolocaux.com/vente/entrepot/lyon',
      'https://www.geolocaux.com/vente/entrepot/bordeaux'
    ];

    for (const searchUrl of searchUrls) {
      try {
        scrapingStatus.currentStep = `Scraping ${searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux'} sur Geolocaux...`;

        await page.goto(searchUrl, { waitUntil: 'networkidle2', timeout: 30000 });

        // Attendre que les annonces se chargent
        await page.waitForSelector('.annonce-item, .property-card, .listing-item', { timeout: 10000 });

        // Extraire les liens des annonces
        const propertyLinks = await page.evaluate(() => {
          const links = [];
          const selectors = [
            'a[href*="/annonce/"]',
            '.annonce-item a',
            '.property-card a',
            '.listing-item a'
          ];

          for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              const href = el.href;
              if (href && href.includes('/annonce/') && !links.includes(href)) {
                links.push(href);
              }
            });
            if (links.length > 0) break;
          }

          return links.slice(0, 10); // Limiter à 10 annonces par ville
        });

        console.log(`📋 ${propertyLinks.length} annonces trouvées sur Geolocaux`);

        // Scraper chaque annonce individuellement
        for (let i = 0; i < propertyLinks.length; i++) {
          const link = propertyLinks[i];
          scrapingStatus.currentStep = `Vérification annonce ${i + 1}/${propertyLinks.length}...`;
          scrapingStatus.progress = 50 + (i / propertyLinks.length) * 50; // 50-100% pour Geolocaux

          try {
            // Vérifier d'abord la disponibilité
            const isAvailable = await checkAvailability(page, link);
            if (!isAvailable) {
              console.log(`❌ Annonce indisponible: ${link}`);
              continue;
            }

            // Extraire les données de l'annonce
            const propertyData = await page.evaluate(() => {
              const getText = (selector) => {
                const el = document.querySelector(selector);
                return el ? el.textContent.trim() : '';
              };

              return {
                title: getText('h1, .property-title, .annonce-title') || 'Titre non trouvé',
                description: getText('.description, .property-description, .annonce-description') || '',
                priceText: getText('.price, .property-price, .annonce-price, .prix'),
                address: getText('.address, .property-address, .annonce-address, .adresse') || '',
                surfaceText: getText('.surface, .property-surface, .superficie'),
                terrainText: getText('.terrain, .foncier, .land-area')
              };
            });

            // Traiter les données extraites
            const price = extractPrice(propertyData.priceText);
            const buildingArea = extractArea(propertyData.surfaceText);
            const landArea = extractArea(propertyData.terrainText);

            // Vérifier si l'annonce correspond aux critères
            if (price >= SEARCH_CRITERIA.minPrice && price <= SEARCH_CRITERIA.maxPrice &&
                buildingArea >= SEARCH_CRITERIA.minBuildingArea) {

              const property = {
                id: `geolocaux-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: propertyData.title,
                description: propertyData.description,
                price: price,
                city: link.includes('lyon') ? 'Lyon' : 'Bordeaux',
                address: propertyData.address,
                buildingArea: buildingArea,
                landArea: landArea,
                source: 'geolocaux',
                sourceUrl: link,
                publishedAt: new Date(), // À améliorer avec vraie date
                scrapedAt: new Date(),
                isNew: true,
                isAvailable: true,
                matchScore: calculateMatchScore({ price, buildingArea, landArea, city: link.includes('lyon') ? 'Lyon' : 'Bordeaux' })
              };

              properties.push(property);
              console.log(`✅ Annonce ajoutée: ${property.title} - ${property.price}€`);
            }

          } catch (error) {
            console.log(`❌ Erreur scraping ${link}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        console.log(`❌ Erreur scraping Geolocaux: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ Erreur générale Geolocaux: ${error.message}`);
  } finally {
    await page.close();
  }

  return properties;
}

// Fonction pour calculer le score de correspondance
function calculateMatchScore(property) {
  let score = 0;

  // Prix (30 points)
  if (property.price >= SEARCH_CRITERIA.minPrice && property.price <= SEARCH_CRITERIA.maxPrice) {
    score += 30;
  } else if (property.price <= SEARCH_CRITERIA.maxPrice * 1.1) {
    score += 20;
  }

  // Superficie bâtiment (25 points)
  if (property.buildingArea >= SEARCH_CRITERIA.minBuildingArea) {
    score += 25;
    if (property.buildingArea >= SEARCH_CRITERIA.idealBuildingArea) {
      score += 5; // Bonus
    }
  }

  // Ville (20 points)
  if (SEARCH_CRITERIA.cities.some(city => property.city.toLowerCase().includes(city.toLowerCase()))) {
    score += 20;
  }

  // Terrain (15 points)
  if (property.landArea >= SEARCH_CRITERIA.minLandArea) {
    score += 15;
  }

  // Bonus pour caractéristiques (10 points)
  const desc = property.description ? property.description.toLowerCase() : '';
  if (desc.includes('poids lourd')) score += 3;
  if (desc.includes('quai')) score += 3;
  if (desc.includes('bureau')) score += 2;
  if (desc.includes('stockage')) score += 2;

  return Math.min(score, 100);
}

// Fonction principale de scraping
async function runLiveScraping() {
  if (scrapingStatus.isRunning) {
    console.log('⚠️ Scraping déjà en cours...');
    return { success: false, message: 'Scraping déjà en cours' };
  }

  scrapingStatus.isRunning = true;
  scrapingStatus.currentStep = 'Initialisation...';
  scrapingStatus.progress = 0;

  console.log('🚀 Démarrage du scraping LIVE...');
  liveProperties = []; // Reset

  let browser;
  try {
    // Lancer Puppeteer
    scrapingStatus.currentStep = 'Lancement du navigateur...';
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    // Scraper les deux sources en parallèle
    const [selogerProps, geolocauxProps] = await Promise.all([
      scrapeSeLoger(browser),
      scrapeGeolocaux(browser)
    ]);

    // Combiner et trier par score
    liveProperties = [...selogerProps, ...geolocauxProps]
      .filter(prop => prop.matchScore >= 70) // Seuil minimum
      .sort((a, b) => b.matchScore - a.matchScore);

    scrapingStatus.currentStep = 'Terminé !';
    scrapingStatus.progress = 100;

    console.log(`✅ Scraping LIVE terminé: ${liveProperties.length} propriétés trouvées et vérifiées`);

    return {
      success: true,
      propertiesFound: liveProperties.length,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Erreur scraping LIVE:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    };
  } finally {
    if (browser) {
      await browser.close();
    }
    scrapingStatus.isRunning = false;
    scrapingStatus.progress = 0;
  }
}

// Fonction utilitaire pour le temps écoulé
function getTimeAgo(date) {
  const diffMs = Date.now() - date;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

  if (diffMins < 60) {
    return `il y a ${diffMins} min`;
  } else {
    return `il y a ${diffHours}h`;
  }
}

// Interface web pour afficher les résultats LIVE
function generateLiveHTML() {
  const stats = {
    total: liveProperties.length,
    new: liveProperties.filter(p => p.isNew).length,
    avgPrice: liveProperties.length > 0 ?
      Math.round(liveProperties.reduce((sum, p) => sum + p.price, 0) / liveProperties.length) : 0,
    avgScore: liveProperties.length > 0 ?
      Math.round(liveProperties.reduce((sum, p) => sum + p.matchScore, 0) / liveProperties.length) : 0
  };

  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Industrial Property Scraper - LIVE</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc; color: #334155; line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white; padding: 30px; text-align: center; margin-bottom: 30px; border-radius: 12px;
        }
        .live-indicator {
            display: inline-flex; align-items: center; gap: 8px; margin-top: 10px;
            background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px;
        }
        .live-dot {
            width: 8px; height: 8px; background: #22c55e; border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .stats {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px; margin-bottom: 30px;
        }
        .stat-card {
            background: white; padding: 20px; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #dc2626; }
        .property-card {
            background: white; border-radius: 12px; padding: 24px; margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: transform 0.2s;
            border-left: 4px solid #22c55e;
        }
        .property-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .property-header { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; }
        .property-title { font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 5px; }
        .property-price { font-size: 1.5rem; font-weight: bold; color: #dc2626; }
        .badges { display: flex; gap: 8px; margin-bottom: 10px; flex-wrap: wrap; }
        .badge {
            padding: 4px 12px; border-radius: 20px; font-size: 0.75rem; font-weight: 500;
        }
        .badge-primary { background: #dbeafe; color: #1d4ed8; }
        .badge-success { background: #dcfce7; color: #166534; }
        .badge-live { background: #fef3c7; color: #92400e; }
        .badge-verified { background: #dcfce7; color: #166534; }
        .property-details {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0;
        }
        .detail-item { display: flex; align-items: center; gap: 8px; font-size: 0.9rem; }
        .btn {
            padding: 12px 24px; border: none; border-radius: 6px; font-weight: 500;
            cursor: pointer; transition: all 0.2s; margin: 5px; text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #dc2626; color: white; }
        .btn-primary:hover { background: #b91c1c; }
        .btn-secondary { background: #f1f5f9; color: #475569; }
        .btn-success { background: #22c55e; color: white; }
        .btn-success:hover { background: #16a34a; }
        .scraping-control {
            background: white; padding: 25px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; text-align: center;
        }
        .progress-section {
            background: white; padding: 25px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px;
            display: ${scrapingStatus.isRunning ? 'block' : 'none'};
        }
        .progress-bar {
            width: 100%; height: 8px; background: #e2e8f0; border-radius: 4px;
            overflow: hidden; margin: 15px 0;
        }
        .progress-fill {
            height: 100%; background: #22c55e; width: ${scrapingStatus.progress}%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Industrial Property Scraper</h1>
            <p>Agent de scraping LIVE - Annonces réelles vérifiées</p>
            <div class="live-indicator">
                <div class="live-dot"></div>
                <span>LIVE - Données en temps réel</span>
            </div>
        </div>

        <div class="scraping-control">
            <h3>🤖 Agent de scraping LIVE</h3>
            <p style="margin: 15px 0; color: #64748b;">
                L'agent navigue en temps réel sur les sites, vérifie la disponibilité et extrait uniquement les annonces actives
            </p>
            <button onclick="startLiveScraping()" class="btn btn-success" ${scrapingStatus.isRunning ? 'disabled' : ''}>
                ${scrapingStatus.isRunning ? '⏳ Scraping en cours...' : '🔄 Lancer le scraping LIVE'}
            </button>
            <div id="status" style="margin-top: 15px; color: #dc2626;">
                ✅ Dernière vérification: ${new Date().toLocaleTimeString('fr-FR')}
            </div>
        </div>

        ${scrapingStatus.isRunning ? `
        <div class="progress-section">
            <h3>Scraping en cours...</h3>
            <div id="currentStep">${scrapingStatus.currentStep}</div>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div style="color: #64748b; font-size: 0.9rem;">
                Vérification de la disponibilité de chaque annonce...
            </div>
        </div>
        ` : ''}

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${stats.total}</div>
                <div class="stat-label">Annonces LIVE vérifiées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.new}</div>
                <div class="stat-label">Nouvelles aujourd'hui</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgPrice.toLocaleString('fr-FR')}€</div>
                <div class="stat-label">Prix moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgScore}%</div>
                <div class="stat-label">Score moyen</div>
            </div>
        </div>

        <div class="properties">
            ${liveProperties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div>
                            <div class="property-title">${property.title}</div>
                            <div class="badges">
                                <span class="badge badge-primary">${property.source}</span>
                                <span class="badge badge-success">${property.matchScore}% match</span>
                                <span class="badge badge-live">🔴 LIVE</span>
                                <span class="badge badge-verified">✅ Vérifiée</span>
                            </div>
                        </div>
                        <div class="property-price">${property.price.toLocaleString('fr-FR')} €</div>
                    </div>

                    <div style="color: #64748b; margin-bottom: 15px;">
                        ${property.description}
                    </div>

                    <div class="property-details">
                        <div class="detail-item">
                            <span>📍</span>
                            <span>${property.address || property.city}</span>
                        </div>
                        <div class="detail-item">
                            <span>📐</span>
                            <span>${property.buildingArea}m² bâtiment</span>
                        </div>
                        ${property.landArea ? `
                        <div class="detail-item">
                            <span>🏞️</span>
                            <span>${property.landArea}m² terrain</span>
                        </div>
                        ` : ''}
                        <div class="detail-item">
                            <span>🔍</span>
                            <span>Scrapé ${getTimeAgo(property.scrapedAt)}</span>
                        </div>
                    </div>

                    <div style="margin-top: 20px; text-align: center;">
                        <a href="${property.sourceUrl}" target="_blank" class="btn btn-primary">
                            🔗 Voir l'annonce LIVE
                        </a>
                        <button class="btn btn-secondary" onclick="alert('Propriété sauvegardée!')">
                            💾 Sauvegarder
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>

        ${liveProperties.length === 0 ? `
            <div style="text-align: center; padding: 40px; background: white; border-radius: 12px;">
                <h3>🔍 ${scrapingStatus.isRunning ? 'Scraping en cours...' : 'Aucune propriété trouvée'}</h3>
                <p style="color: #64748b; margin: 15px 0;">
                    ${scrapingStatus.isRunning ?
                      'L\'agent recherche et vérifie les annonces en temps réel...' :
                      'L\'agent n\'a pas encore trouvé de propriétés correspondant à vos critères et disponibles.'
                    }
                </p>
                ${!scrapingStatus.isRunning ? `
                <button onclick="startLiveScraping()" class="btn btn-success">
                    🚀 Lancer le scraping LIVE
                </button>
                ` : ''}
            </div>
        ` : ''}
    </div>

    <script>
        async function startLiveScraping() {
            const status = document.getElementById('status');
            status.innerHTML = '🔄 Lancement du scraping LIVE...';
            status.style.color = '#f59e0b';

            try {
                const response = await fetch('/api/scraping/live', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    status.innerHTML = '✅ Scraping terminé - Rechargement...';
                    status.style.color = '#22c55e';
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    status.innerHTML = '❌ Erreur lors du scraping: ' + (result.error || result.message);
                    status.style.color = '#dc2626';
                }
            } catch (error) {
                status.innerHTML = '❌ Erreur de connexion';
                status.style.color = '#dc2626';
            }
        }

        // Auto-refresh pendant le scraping
        ${scrapingStatus.isRunning ? `
        setInterval(() => {
            if (window.location.pathname === '/') {
                window.location.reload();
            }
        }, 5000);
        ` : ''}
    </script>
</body>
</html>
  `;
}

// Serveur HTTP
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(generateLiveHTML());
  } else if (url.pathname === '/api/scraping/live' && req.method === 'POST') {
    const result = await runLiveScraping();
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(result));
  } else if (url.pathname === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      isRunning: scrapingStatus.isRunning,
      currentStep: scrapingStatus.currentStep,
      progress: scrapingStatus.progress,
      propertiesCount: liveProperties.length
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

// Démarrage
server.listen(PORT, () => {
  console.log(`🚀 Agent de scraping LIVE démarré sur http://localhost:${PORT}`);
  console.log('🔴 Mode LIVE activé - Vérification de disponibilité en temps réel');
  console.log('✅ Prêt ! Ouvrez http://localhost:3000 pour lancer le scraping');
});