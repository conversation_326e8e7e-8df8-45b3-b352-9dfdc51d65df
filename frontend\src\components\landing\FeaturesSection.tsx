import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Zap,
  Shield,
  Rocket,
  BarChart3,
  Workflow,
  Code,
  Cloud,
  Users,
  Lock,
  Gauge,
  Puzzle,
  Bot
} from 'lucide-react'
import { Card, CardContent } from '../ui/Card'
import { Badge } from '../ui/Badge'
import { Tooltip } from '../ui/Tooltip'
import { cn } from '../../lib/utils'

export function FeaturesSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const features = [
    {
      icon: Workflow,
      title: "Automatisations visuelles",
      description: "Créez des workflows complexes avec notre éditeur drag & drop intuitif. Aucun code requis.",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      icon: Zap,
      title: "Exécution ultra-rapide",
      description: "Moteur d'exécution optimisé pour traiter des millions d'opérations par seconde.",
      gradient: "from-yellow-500 to-orange-500",
    },
    {
      icon: Shield,
      title: "Sécurité enterprise",
      description: "Chiffrement end-to-end, authentification SSO et conformité SOC2 Type II.",
      gradient: "from-green-500 to-emerald-500",
    },
    {
      icon: Puzzle,
      title: "500+ Intégrations",
      description: "Connectez tous vos outils favoris : CRM, ERP, APIs, bases de données et plus.",
      gradient: "from-purple-500 to-violet-500",
    },
    {
      icon: BarChart3,
      title: "Analytics avancées",
      description: "Tableaux de bord en temps réel pour monitorer et optimiser vos automatisations.",
      gradient: "from-pink-500 to-rose-500",
    },
    {
      icon: Bot,
      title: "IA intégrée",
      description: "Assistant IA pour optimiser vos workflows et suggérer des améliorations.",
      gradient: "from-indigo-500 to-purple-500",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="features" ref={ref} className="py-32 relative bg-white">
      {/* Modern Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-neutral-50 to-white"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Modern Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <Badge
              variant="primary"
              size="lg"
              className="bg-brand-100 text-brand-800 border border-brand-200"
            >
              ⚡ Fonctionnalités
            </Badge>
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-6xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-gradient-to-r from-neutral-900 to-neutral-700 bg-clip-text text-transparent">
              Tout ce dont vous avez besoin
            </span>
            <br />
            <span className="bg-gradient-to-r from-brand-600 to-brand-700 bg-clip-text text-transparent">
              pour automatiser
            </span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-xl text-neutral-600 max-w-3xl mx-auto font-medium"
          >
            Une plateforme complète avec tous les outils nécessaires pour créer,
            déployer et gérer vos automatisations à l'échelle enterprise.
          </motion.p>
        </motion.div>

        {/* Modern Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
            >
              <Tooltip content={`En savoir plus sur ${feature.title}`} position="top">
                <Card
                  interactive
                  className="h-full group hover:shadow-glow transition-all duration-300"
                  padding="lg"
                >
                  {/* Modern Icon */}
                  <div className={cn(
                    "w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-300",
                    "bg-gradient-to-r", feature.gradient,
                    "group-hover:scale-110 group-hover:shadow-lg"
                  )}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-neutral-900 mb-4 group-hover:text-brand-700 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-neutral-600 leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              </Tooltip>
            </motion.div>
          ))}
        </motion.div>

        {/* Modern Bottom CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-20"
        >
          <Card className="bg-gradient-to-r from-brand-50 to-brand-100/50 border-brand-200 max-w-2xl mx-auto" padding="lg">
            <div className="flex items-center justify-center space-x-3 text-brand-700">
              <span className="text-lg font-medium">Et bien plus encore...</span>
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="text-xl"
              >
                →
              </motion.div>
            </div>
            <p className="text-brand-600 mt-2 text-sm">
              Découvrez toutes nos fonctionnalités avancées
            </p>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
