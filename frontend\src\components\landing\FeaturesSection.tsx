import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  Zap,
  Shield,
  Rocket,
  BarChart3,
  Workflow,
  Code,
  Cloud,
  Users,
  Lock,
  Gauge,
  Puzzle,
  Bot
} from 'lucide-react'
import { Card, CardContent } from '../ui/Card'
import { Badge } from '../ui/Badge'
import { Tooltip } from '../ui/Tooltip'
import { cn } from '../../lib/utils'

export function FeaturesSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const features = [
    {
      icon: Workflow,
      title: "Elite Orchestration",
      description: "Command sophisticated workflows with our intuitive visual composer. No coding required for digital mastery.",
      gradient: "from-amber-400 to-amber-500",
    },
    {
      icon: Zap,
      title: "Lightning Execution",
      description: "Proprietary engine processes millions of operations with unparalleled speed and precision.",
      gradient: "from-amber-500 to-amber-600",
    },
    {
      icon: Shield,
      title: "Fortress Security",
      description: "Military-grade encryption, enterprise SSO, and SOC2 Type II compliance for absolute protection.",
      gradient: "from-slate-400 to-slate-500",
    },
    {
      icon: Puzzle,
      title: "Infinite Connections",
      description: "Seamlessly integrate with 500+ premium platforms: CRM, ERP, APIs, databases, and beyond.",
      gradient: "from-amber-400 to-amber-500",
    },
    {
      icon: BarChart3,
      title: "Intelligence Analytics",
      description: "Real-time dashboards with predictive insights to optimize and elevate your automation empire.",
      gradient: "from-slate-400 to-slate-500",
    },
    {
      icon: Bot,
      title: "AI Concierge",
      description: "Personal AI advisor that continuously optimizes workflows and suggests strategic improvements.",
      gradient: "from-amber-500 to-amber-600",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section id="capabilities" ref={ref} className="py-32 relative bg-gradient-to-b from-slate-950 via-slate-900 to-black">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-slate-950 via-slate-900 to-black"></div>
        {/* Subtle ethereal glow */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-400/10 to-amber-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-slate-400/8 to-slate-500/8 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Elite Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
                Elite Capabilities
              </span>
            </div>
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-6xl font-black mb-6 tracking-tight"
          >
            <span className="block text-white mb-2">
              Everything You Need to
            </span>
            <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
              Dominate Digitally
            </span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-400 max-w-3xl mx-auto font-light leading-relaxed"
          >
            A comprehensive platform with all the sophisticated tools necessary to create,
            deploy, and manage your automation empire at enterprise scale.
          </motion.p>
        </motion.div>

        {/* Modern Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
            >
              <Tooltip content={`Discover ${feature.title}`} position="top">
                <Card
                  interactive
                  className="h-full group hover:shadow-2xl hover:shadow-amber-400/20 transition-all duration-500 bg-black/60 backdrop-blur-xl border border-slate-700/50 hover:border-amber-400/30"
                  padding="lg"
                >
                  {/* Elite Icon */}
                  <div className={cn(
                    "w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-500",
                    "bg-gradient-to-r", feature.gradient,
                    "group-hover:scale-110 group-hover:shadow-xl shadow-lg"
                  )}>
                    <feature.icon className="w-8 h-8 text-black" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-amber-400 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-slate-400 leading-relaxed font-light">
                    {feature.description}
                  </p>
                </Card>
              </Tooltip>
            </motion.div>
          ))}
        </motion.div>

        {/* Elite Bottom CTA */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mt-20"
        >
          <Card className="bg-gradient-to-r from-black/60 to-slate-900/60 backdrop-blur-xl border-amber-400/20 max-w-2xl mx-auto shadow-2xl shadow-amber-400/10" padding="lg">
            <div className="flex items-center justify-center space-x-3 text-amber-400">
              <span className="text-lg font-semibold">And much more awaits...</span>
              <motion.div
                animate={{ x: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="text-xl"
              >
                →
              </motion.div>
            </div>
            <p className="text-slate-400 mt-2 text-sm font-light">
              Discover all our advanced elite capabilities
            </p>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
