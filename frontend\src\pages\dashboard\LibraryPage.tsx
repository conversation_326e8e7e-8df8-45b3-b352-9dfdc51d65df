import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Search, 
  Filter, 
  Star, 
  Download, 
  Eye,
  Workflow,
  Mail,
  ShoppingCart,
  Database,
  Slack,
  Calendar,
  FileText,
  Users,
  BarChart3,
  Zap
} from 'lucide-react'

export function LibraryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')

  const categories = [
    { id: 'all', name: 'Toutes les catégories', count: 47 },
    { id: 'marketing', name: 'Marketing', count: 12 },
    { id: 'sales', name: 'Ventes', count: 8 },
    { id: 'ecommerce', name: 'E-commerce', count: 9 },
    { id: 'productivity', name: 'Productivité', count: 11 },
    { id: 'customer-service', name: 'Service client', count: 7 }
  ]

  const templates = [
    {
      id: 1,
      name: 'Lead Qualification Automatique',
      description: 'Qualifiez automatiquement vos leads entrants et assignez-les aux bonnes équipes',
      category: 'marketing',
      icon: Users,
      color: 'from-purple-500 to-pink-500',
      rating: 4.8,
      downloads: 1247,
      tags: ['CRM', 'Lead Generation', 'Scoring'],
      complexity: 'Intermédiaire',
      estimatedTime: '15 min'
    },
    {
      id: 2,
      name: 'Sync E-commerce Multi-plateformes',
      description: 'Synchronisez vos stocks et commandes entre Shopify, WooCommerce et votre ERP',
      category: 'ecommerce',
      icon: ShoppingCart,
      color: 'from-blue-500 to-cyan-500',
      rating: 4.9,
      downloads: 856,
      tags: ['Shopify', 'WooCommerce', 'Inventory'],
      complexity: 'Avancé',
      estimatedTime: '30 min'
    },
    {
      id: 3,
      name: 'Onboarding Client Personnalisé',
      description: 'Créez une séquence d\'accueil personnalisée pour vos nouveaux clients',
      category: 'customer-service',
      icon: Mail,
      color: 'from-green-500 to-emerald-500',
      rating: 4.7,
      downloads: 634,
      tags: ['Email', 'Customer Success', 'Automation'],
      complexity: 'Débutant',
      estimatedTime: '10 min'
    },
    {
      id: 4,
      name: 'Notifications Slack Intelligentes',
      description: 'Recevez des alertes Slack contextuelles pour vos événements importants',
      category: 'productivity',
      icon: Slack,
      color: 'from-yellow-500 to-orange-500',
      rating: 4.6,
      downloads: 923,
      tags: ['Slack', 'Notifications', 'Alerts'],
      complexity: 'Débutant',
      estimatedTime: '5 min'
    },
    {
      id: 5,
      name: 'Backup Automatique des Données',
      description: 'Sauvegardez automatiquement vos données critiques vers le cloud',
      category: 'productivity',
      icon: Database,
      color: 'from-indigo-500 to-purple-500',
      rating: 4.5,
      downloads: 445,
      tags: ['Backup', 'Security', 'Cloud'],
      complexity: 'Intermédiaire',
      estimatedTime: '20 min'
    },
    {
      id: 6,
      name: 'Suivi des Performances Ventes',
      description: 'Analysez et reportez automatiquement vos métriques de vente',
      category: 'sales',
      icon: BarChart3,
      color: 'from-pink-500 to-rose-500',
      rating: 4.8,
      downloads: 567,
      tags: ['Analytics', 'Sales', 'Reporting'],
      complexity: 'Intermédiaire',
      estimatedTime: '25 min'
    }
  ]

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Débutant':
        return 'bg-green-100 text-green-800'
      case 'Intermédiaire':
        return 'bg-yellow-100 text-yellow-800'
      case 'Avancé':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-slate-900 mb-2">
          Bibliothèque d'automatisations
        </h1>
        <p className="text-slate-600">
          Découvrez et utilisez des templates d'automatisation prêts à l'emploi
        </p>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-2xl border border-slate-200 p-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
            <input
              type="text"
              placeholder="Rechercher un template..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setCategoryFilter(category.id)}
                className={`px-4 py-2 rounded-xl text-sm font-medium transition-all ${
                  categoryFilter === category.id
                    ? 'bg-purple-500 text-white'
                    : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-300 group"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${template.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform`}>
                <template.icon className="w-6 h-6 text-white" />
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm font-medium text-slate-600">
                  {template.rating}
                </span>
              </div>
            </div>

            {/* Content */}
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {template.name}
            </h3>
            <p className="text-slate-600 text-sm mb-4 line-clamp-2">
              {template.description}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {template.tags.slice(0, 2).map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg"
                >
                  {tag}
                </span>
              ))}
              {template.tags.length > 2 && (
                <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg">
                  +{template.tags.length - 2}
                </span>
              )}
            </div>

            {/* Metadata */}
            <div className="flex items-center justify-between mb-4">
              <span className={`text-xs font-medium px-2 py-1 rounded-full ${getComplexityColor(template.complexity)}`}>
                {template.complexity}
              </span>
              <span className="text-xs text-slate-500">
                ~{template.estimatedTime}
              </span>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between mb-4 text-sm text-slate-500">
              <div className="flex items-center space-x-1">
                <Download className="w-4 h-4" />
                <span>{template.downloads.toLocaleString()} utilisations</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <button className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white py-2 px-4 rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105">
                Utiliser ce template
              </button>
              <button className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-xl transition-colors">
                <Eye className="w-5 h-5" />
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Workflow className="w-16 h-16 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-slate-900 mb-2">
            Aucun template trouvé
          </h3>
          <p className="text-slate-600 mb-6">
            Essayez de modifier vos critères de recherche ou explorez d'autres catégories.
          </p>
        </div>
      )}

      {/* Featured Section */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              Besoin d'un template personnalisé ?
            </h2>
            <p className="text-purple-100 mb-4">
              Notre équipe peut créer des automatisations sur mesure pour vos besoins spécifiques.
            </p>
            <button className="bg-white text-purple-600 px-6 py-3 rounded-xl font-semibold hover:bg-purple-50 transition-colors">
              Nous contacter
            </button>
          </div>
          <div className="hidden lg:block">
            <Zap className="w-24 h-24 text-purple-200" />
          </div>
        </div>
      </div>
    </div>
  )
}
