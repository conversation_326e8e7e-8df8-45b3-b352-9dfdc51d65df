import { forwardRef, HTMLAttributes } from 'react'
import { motion } from 'framer-motion'
import { User } from 'lucide-react'
import { cn, getInitials } from '../../lib/utils'

// Avatar variants
const avatarVariants = {
  // Size variants
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-base',
  xl: 'w-16 h-16 text-lg',
  '2xl': 'w-20 h-20 text-xl',
  
  // Shape variants
  circle: 'rounded-full',
  square: 'rounded-lg',
  
  // Status variants
  online: 'ring-2 ring-success-500 ring-offset-2',
  offline: 'ring-2 ring-neutral-300 ring-offset-2',
  busy: 'ring-2 ring-error-500 ring-offset-2',
  away: 'ring-2 ring-warning-500 ring-offset-2',
}

export interface AvatarProps extends HTMLAttributes<HTMLDivElement> {
  src?: string
  alt?: string
  name?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  shape?: 'circle' | 'square'
  status?: 'online' | 'offline' | 'busy' | 'away'
  showStatus?: boolean
  fallbackIcon?: React.ReactNode
  loading?: boolean
}

export const Avatar = forwardRef<HTMLDivElement, AvatarProps>(
  ({ 
    className,
    src,
    alt,
    name,
    size = 'md',
    shape = 'circle',
    status,
    showStatus = false,
    fallbackIcon,
    loading = false,
    ...props 
  }, ref) => {
    const baseClasses = cn(
      // Base styles
      'relative inline-flex items-center justify-center overflow-hidden bg-neutral-100 font-medium text-neutral-600 transition-all duration-200',
      
      // Size styles
      avatarVariants[size],
      
      // Shape styles
      avatarVariants[shape],
      
      // Status ring
      showStatus && status && avatarVariants[status],
      
      className
    )

    const statusDotSize = {
      xs: 'w-1.5 h-1.5',
      sm: 'w-2 h-2',
      md: 'w-2.5 h-2.5',
      lg: 'w-3 h-3',
      xl: 'w-3.5 h-3.5',
      '2xl': 'w-4 h-4',
    }

    const statusColors = {
      online: 'bg-success-500',
      offline: 'bg-neutral-400',
      busy: 'bg-error-500',
      away: 'bg-warning-500',
    }

    const renderContent = () => {
      if (loading) {
        return (
          <div className="animate-pulse bg-neutral-200 w-full h-full rounded-inherit" />
        )
      }

      if (src) {
        return (
          <img
            src={src}
            alt={alt || name || 'Avatar'}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Hide image on error and show fallback
              e.currentTarget.style.display = 'none'
            }}
          />
        )
      }

      if (name) {
        return (
          <span className="font-semibold">
            {getInitials(name)}
          </span>
        )
      }

      if (fallbackIcon) {
        return fallbackIcon
      }

      return <User className="w-1/2 h-1/2" />
    }

    return (
      <motion.div
        ref={ref}
        className={baseClasses}
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        {...props}
      >
        {renderContent()}
        
        {/* Status Indicator */}
        {showStatus && status && (
          <span 
            className={cn(
              'absolute bottom-0 right-0 block rounded-full ring-2 ring-white',
              statusDotSize[size],
              statusColors[status]
            )}
            aria-label={`Status: ${status}`}
          />
        )}
      </motion.div>
    )
  }
)

Avatar.displayName = 'Avatar'

// Avatar Group component for displaying multiple avatars
export interface AvatarGroupProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  max?: number
  size?: AvatarProps['size']
  spacing?: 'tight' | 'normal' | 'loose'
}

export const AvatarGroup = forwardRef<HTMLDivElement, AvatarGroupProps>(
  ({ 
    className,
    children,
    max = 5,
    size = 'md',
    spacing = 'normal',
    ...props 
  }, ref) => {
    const spacingClasses = {
      tight: '-space-x-1',
      normal: '-space-x-2',
      loose: '-space-x-1',
    }

    const childrenArray = Array.isArray(children) ? children : [children]
    const visibleChildren = childrenArray.slice(0, max)
    const remainingCount = Math.max(0, childrenArray.length - max)

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center',
          spacingClasses[spacing],
          className
        )}
        {...props}
      >
        {visibleChildren.map((child, index) => (
          <div key={index} className="relative">
            {child}
          </div>
        ))}
        
        {remainingCount > 0 && (
          <Avatar
            size={size}
            className="bg-neutral-200 text-neutral-600 border-2 border-white"
          >
            +{remainingCount}
          </Avatar>
        )}
      </div>
    )
  }
)

AvatarGroup.displayName = 'AvatarGroup'
