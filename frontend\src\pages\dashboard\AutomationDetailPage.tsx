import { useParams } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Play, 
  Pause, 
  Settings, 
  BarChart3, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  Activity,
  Zap
} from 'lucide-react'
import { Link } from 'react-router-dom'

export function AutomationDetailPage() {
  const { id } = useParams()

  // Mock data - in real app, fetch based on id
  const automation = {
    id: 1,
    name: 'Traitement automatique des leads',
    description: 'Capture et qualification des leads depuis le site web avec scoring automatique',
    status: 'active',
    createdAt: '2024-01-15',
    lastRun: '2 minutes ago',
    nextRun: 'Dans 5 minutes',
    executions: 1247,
    successRate: 98.2,
    avgExecutionTime: '2.3s',
    trigger: 'Webhook',
    tags: ['CRM', 'Marketing', 'Lead Generation']
  }

  const recentExecutions = [
    { id: 1, status: 'success', startTime: '14:32:15', duration: '2.1s', trigger: 'Webhook' },
    { id: 2, status: 'success', startTime: '14:30:42', duration: '1.8s', trigger: 'Webhook' },
    { id: 3, status: 'success', startTime: '14:28:33', duration: '2.5s', trigger: 'Webhook' },
    { id: 4, status: 'error', startTime: '14:25:12', duration: '0.3s', trigger: 'Webhook' },
    { id: 5, status: 'success', startTime: '14:23:45', duration: '2.0s', trigger: 'Webhook' }
  ]

  const stats = [
    {
      name: 'Exécutions totales',
      value: automation.executions.toLocaleString(),
      icon: Activity,
      color: 'from-purple-500 to-pink-500'
    },
    {
      name: 'Taux de succès',
      value: `${automation.successRate}%`,
      icon: CheckCircle,
      color: 'from-green-500 to-emerald-500'
    },
    {
      name: 'Temps moyen',
      value: automation.avgExecutionTime,
      icon: Clock,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Dernière exécution',
      value: automation.lastRun,
      icon: Calendar,
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/dashboard/automations"
            className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-xl transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-slate-900">
              {automation.name}
            </h1>
            <p className="text-slate-600 mt-1">
              {automation.description}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="flex items-center px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-xl transition-colors">
            <Settings className="w-5 h-5 mr-2" />
            Configurer
          </button>
          {automation.status === 'active' ? (
            <button className="flex items-center px-4 py-2 bg-yellow-500 text-white rounded-xl hover:bg-yellow-600 transition-colors">
              <Pause className="w-5 h-5 mr-2" />
              Mettre en pause
            </button>
          ) : (
            <button className="flex items-center px-4 py-2 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors">
              <Play className="w-5 h-5 mr-2" />
              Démarrer
            </button>
          )}
        </div>
      </div>

      {/* Status Banner */}
      <div className={`p-4 rounded-xl border ${
        automation.status === 'active' 
          ? 'bg-green-50 border-green-200' 
          : 'bg-yellow-50 border-yellow-200'
      }`}>
        <div className="flex items-center space-x-3">
          {automation.status === 'active' ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <Clock className="w-5 h-5 text-yellow-600" />
          )}
          <div>
            <p className={`font-medium ${
              automation.status === 'active' ? 'text-green-900' : 'text-yellow-900'
            }`}>
              Automatisation {automation.status === 'active' ? 'active' : 'en pause'}
            </p>
            <p className={`text-sm ${
              automation.status === 'active' ? 'text-green-700' : 'text-yellow-700'
            }`}>
              {automation.status === 'active' 
                ? `Prochaine exécution: ${automation.nextRun}`
                : 'L\'automatisation est actuellement en pause'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl p-6 border border-slate-200"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
            <div className="text-2xl font-bold text-slate-900 mb-1">
              {stat.value}
            </div>
            <div className="text-sm text-slate-600">
              {stat.name}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Executions */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl border border-slate-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-slate-900">
                Exécutions récentes
              </h2>
              <Link
                to={`/dashboard/automations/${id}/logs`}
                className="text-purple-600 hover:text-purple-700 text-sm font-medium"
              >
                Voir tous les logs
              </Link>
            </div>
            
            <div className="space-y-4">
              {recentExecutions.map((execution) => (
                <div
                  key={execution.id}
                  className="flex items-center justify-between p-4 bg-slate-50 rounded-xl"
                >
                  <div className="flex items-center space-x-4">
                    {execution.status === 'success' ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-red-600" />
                    )}
                    <div>
                      <p className="font-medium text-slate-900">
                        {execution.status === 'success' ? 'Succès' : 'Erreur'}
                      </p>
                      <p className="text-sm text-slate-600">
                        {execution.startTime} • Durée: {execution.duration}
                      </p>
                    </div>
                  </div>
                  <span className="text-xs text-slate-500 bg-slate-200 px-2 py-1 rounded-lg">
                    {execution.trigger}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Automation Info */}
        <div className="space-y-6">
          {/* Details */}
          <div className="bg-white rounded-2xl border border-slate-200 p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              Détails
            </h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-600">Déclencheur</label>
                <p className="text-slate-900">{automation.trigger}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-slate-600">Créé le</label>
                <p className="text-slate-900">{automation.createdAt}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-slate-600">Tags</label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {automation.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-2xl border border-slate-200 p-6">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              Actions rapides
            </h3>
            <div className="space-y-3">
              <button className="w-full flex items-center justify-center px-4 py-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors">
                <Zap className="w-5 h-5 mr-2" />
                Exécuter maintenant
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-colors">
                <BarChart3 className="w-5 h-5 mr-2" />
                Voir les analytics
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 transition-colors">
                <Settings className="w-5 h-5 mr-2" />
                Modifier la configuration
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
