import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from 'react-router-dom'
import {
  ArrowRight,
  Zap,
  Shield,
  Rocket,
  Users,
  BarChart3,
  Workflow,
  Star,
  CheckCircle,
  Play
} from 'lucide-react'
import { LandingHeader } from '../components/landing/LandingHeader'
import { HeroSection } from '../components/landing/HeroSection'
import { ProcessSection } from '../components/landing/ProcessSection'
import { FeaturesSection } from '../components/landing/FeaturesSection'
import { AutomationShowcase } from '../components/landing/AutomationShowcase'
import { TestimonialsSection } from '../components/landing/TestimonialsSection'
import { PricingSection } from '../components/landing/PricingSection'
import { CTASection } from '../components/landing/CTASection'
import { Footer } from '../components/landing/Footer'

export function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <LandingHeader />

      {/* Hero Section */}
      <HeroSection />

      {/* Process Section */}
      <ProcessSection />

      {/* Features Section */}
      <FeaturesSection />

      {/* Automation Showcase */}
      <AutomationShowcase />

      {/* Testimonials */}
      <TestimonialsSection />

      {/* Pricing */}
      <PricingSection />

      {/* CTA Section */}
      <CTASection />

      {/* Footer */}
      <Footer />
    </div>
  )
}
