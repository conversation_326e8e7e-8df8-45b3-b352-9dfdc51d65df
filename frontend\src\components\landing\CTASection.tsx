import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from 'react-router-dom'
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Zap } from 'lucide-react'

export function CTASection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section ref={ref} className="py-32 relative overflow-hidden bg-gradient-to-b from-black via-slate-950 to-slate-900">
      {/* Elite Background Effects */}
      <div className="absolute inset-0">
        {/* Elite Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-black to-slate-950/80"></div>

        {/* Elite Animated Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-400/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-slate-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-amber-300/8 rounded-full blur-3xl animate-pulse delay-2000"></div>

        {/* Elite Grid Pattern */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F59E0B' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* Elite Floating Elements */}
      <motion.div
        className="absolute top-20 left-20 hidden lg:block"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <div className="w-20 h-20 bg-gradient-to-r from-amber-400/20 to-amber-500/20 rounded-3xl backdrop-blur-xl border border-amber-400/20 flex items-center justify-center shadow-lg shadow-amber-400/10">
          <Zap className="w-10 h-10 text-amber-400" />
        </div>
      </motion.div>

      <motion.div
        className="absolute bottom-20 right-20 hidden lg:block"
        animate={{
          y: [0, 20, 0],
          rotate: [0, -5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      >
        <div className="w-16 h-16 bg-gradient-to-r from-slate-400/20 to-slate-500/20 rounded-2xl backdrop-blur-xl border border-slate-400/20 flex items-center justify-center shadow-lg shadow-slate-400/10">
          <Sparkles className="w-8 h-8 text-slate-400" />
        </div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        ref={ref}
        variants={containerVariants}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
      >
        {/* Elite Badge */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full shadow-2xl">
            <Sparkles className="w-5 h-5 text-amber-400 mr-3" />
            <span className="text-lg font-medium bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent">
              Join 1,000+ Elite Enterprises
            </span>
          </div>
        </motion.div>

        {/* Elite Main Headline */}
        <motion.h2
          variants={itemVariants}
          className="text-5xl md:text-7xl font-black mb-8 tracking-tight"
        >
          <span className="block text-white mb-2">
            Ready to Ascend Your
          </span>
          <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
            Digital Empire?
          </span>
        </motion.h2>

        {/* Elite Subtitle */}
        <motion.p
          variants={itemVariants}
          className="text-xl md:text-2xl text-slate-400 mb-12 max-w-3xl mx-auto leading-relaxed font-light"
        >
          Begin your complimentary trial today and discover how IMPERIUM can
          revolutionize your operations with unparalleled sophistication.
        </motion.p>

        {/* Elite CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16"
        >
          <Link
            to="/signup"
            className="group relative px-10 py-5 bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-black text-lg font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-400/30 border border-amber-300/50"
          >
            <span className="flex items-center">
              Begin Your Ascension
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" />
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl blur opacity-0 group-hover:opacity-30 transition-opacity -z-10"></div>
          </Link>

          <Link
            to="/demo"
            className="group flex items-center px-10 py-5 bg-black/20 backdrop-blur-xl text-white text-lg font-semibold rounded-2xl border border-slate-600/50 hover:bg-black/30 hover:border-slate-500/50 transition-all duration-300"
          >
            Schedule Consultation
          </Link>
        </motion.div>

        {/* Elite Trust Indicators */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
        >
          {[
            { icon: "⚡", text: "5-Minute Setup" },
            { icon: "🛡️", text: "Fortress Security" },
            { icon: "💎", text: "No Commitment" },
            { icon: "🎯", text: "24/7 Concierge" },
          ].map((item, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl mb-2 filter grayscale hover:grayscale-0 transition-all duration-300">{item.icon}</div>
              <div className="text-slate-400 text-sm font-light">{item.text}</div>
            </div>
          ))}
        </motion.div>

        {/* Elite Bottom Note */}
        <motion.div
          variants={itemVariants}
          className="mt-12"
        >
          <p className="text-slate-400 text-sm font-light">
            14-day complimentary trial • No obligations • Cancel anytime with dignity
          </p>
        </motion.div>
      </motion.div>
    </section>
  )
}
