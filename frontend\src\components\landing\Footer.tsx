import { Link } from 'react-router-dom'
import {
  Zap,
  Twitter,
  Github,
  Linkedin,
  Mail,
  MapPin,
  Phone
} from 'lucide-react'

export function Footer() {
  const footerLinks = {
    platform: [
      { name: 'Capabilities', href: '#capabilities' },
      { name: 'Integrations', href: '/integrations' },
      { name: 'API Access', href: '/api' },
      { name: 'Security', href: '/security' },
      { name: 'Investment', href: '#investment' },
    ],
    empire: [
      { name: 'Our Legacy', href: '/about' },
      { name: 'Chronicles', href: '/blog' },
      { name: 'Join Elite', href: '/careers' },
      { name: 'Press Room', href: '/press' },
      { name: 'Alliances', href: '/partners' },
    ],
    resources: [
      { name: 'Documentation', href: '/docs' },
      { name: 'Elite Guides', href: '/guides' },
      { name: 'Templates', href: '/templates' },
      { name: 'Aristocracy', href: '/community' },
      { name: 'Concierge', href: '/support' },
    ],
    governance: [
      { name: 'Privacy Charter', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Data Protocols', href: '/cookies' },
      { name: 'Compliance', href: '/gdpr' },
    ],
  }

  const socialLinks = [
    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/imperium' },
    { name: 'GitHub', icon: Github, href: 'https://github.com/imperium' },
    { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/company/imperium' },
  ]

  return (
    <footer className="relative bg-gradient-to-b from-slate-950 via-black to-slate-950 border-t border-amber-400/20">
      {/* Elite Background Pattern */}
      <div className="absolute inset-0 opacity-30" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F59E0B' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      {/* Ethereal glow */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-amber-400/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-slate-400/3 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Elite Brand Section */}
            <div className="lg:col-span-2">
              <Link to="/" className="flex items-center space-x-3 mb-6">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 rounded-xl flex items-center justify-center shadow-lg shadow-amber-400/25">
                    <Zap className="w-6 h-6 text-black" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-amber-400 rounded-full animate-pulse shadow-lg shadow-amber-400/50"></div>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-amber-300 to-amber-400 bg-clip-text text-transparent">
                  IMPERIUM
                </span>
              </Link>

              <p className="text-slate-400 mb-6 leading-relaxed font-light">
                The world's most sophisticated automation platform for digital empires.
                Orchestrate your operations with unparalleled precision and elegance.
              </p>

              {/* Elite Contact Info */}
              <div className="space-y-3 text-sm text-slate-400">
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-amber-400" />
                  <span>Geneva, Switzerland</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-amber-400" />
                  <a href="mailto:<EMAIL>" className="hover:text-amber-400 transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-amber-400" />
                  <span>+41 22 123 4567</span>
                </div>
              </div>
            </div>

            {/* Platform Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 tracking-wide">Platform</h3>
              <ul className="space-y-3">
                {footerLinks.platform.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-slate-400 hover:text-amber-400 transition-colors text-sm font-light"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Empire Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 tracking-wide">Empire</h3>
              <ul className="space-y-3">
                {footerLinks.empire.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-slate-400 hover:text-amber-400 transition-colors text-sm font-light"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 tracking-wide">Resources</h3>
              <ul className="space-y-3">
                {footerLinks.resources.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-slate-400 hover:text-amber-400 transition-colors text-sm font-light"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Governance Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 tracking-wide">Governance</h3>
              <ul className="space-y-3">
                {footerLinks.governance.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-slate-400 hover:text-amber-400 transition-colors text-sm font-light"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Elite Bottom Section */}
        <div className="py-8 border-t border-amber-400/20">
          <div className="flex flex-col md:flex-row items-center justify-between">
            {/* Elite Copyright */}
            <div className="text-slate-400 text-sm mb-4 md:mb-0 font-light">
              © 2024 IMPERIUM. All rights reserved. Built for digital aristocracy.
            </div>

            {/* Elite Social Links */}
            <div className="flex items-center space-x-6">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-slate-400 hover:text-amber-400 transition-colors duration-300"
                  aria-label={social.name}
                >
                  <social.icon className="w-5 h-5" />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
