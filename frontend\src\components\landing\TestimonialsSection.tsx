import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Star, Quote } from 'lucide-react'

export function TestimonialsSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const testimonials = [
    {
      name: "Victoria Sterling",
      role: "Chief Executive",
      company: "Sterling Enterprises",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      content: "IMPERIUM has elevated our operations to unprecedented heights. We've orchestrated 90% of our processes and reclaimed 300+ hours monthly for strategic initiatives.",
      rating: 5,
      gradient: "from-amber-400 to-amber-500"
    },
    {
      name: "<PERSON>",
      role: "Managing Director",
      company: "Rothschild Ventures",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      content: "The seamless integration with our legacy systems was executed flawlessly. The concierge support is unparalleled, and the platform's stability is fortress-grade.",
      rating: 5,
      gradient: "from-slate-400 to-slate-500"
    },
    {
      name: "<PERSON>tone",
      role: "Innovation Director",
      company: "Blackstone Labs",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      content: "IMPERIUM liberated us from mundane operations, allowing pure focus on innovation. We achieved 500% ROI within 4 months of implementation.",
      rating: 5,
      gradient: "from-amber-500 to-amber-600"
    },
    {
      name: "Maximilian Von Habsburg",
      role: "Founder & Visionary",
      company: "Habsburg Holdings",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      content: "As an empire builder, I required a platform that scales infinitely. IMPERIUM enabled exponential growth without expanding our operational overhead.",
      rating: 5,
      gradient: "from-amber-400 to-amber-500"
    },
    {
      name: "Isabella Medici",
      role: "Chief Technology Officer",
      company: "Medici Systems",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      content: "The elegance of the interface is remarkable. Even our non-technical executives orchestrate complex automations within minutes of onboarding.",
      rating: 5,
      gradient: "from-slate-400 to-slate-500"
    },
    {
      name: "Constantine Vanderbilt",
      role: "Global Operations Chief",
      company: "Vanderbilt International",
      avatar: "https://images.unsplash.com/photo-**********-0b93528c311a?w=150&h=150&fit=crop&crop=face",
      content: "IMPERIUM standardized our operations across 25 nations. The governance framework and security protocols exceed institutional banking standards.",
      rating: 5,
      gradient: "from-amber-500 to-amber-600"
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.6, -0.05, 0.01, 0.99],
      },
    },
  }

  return (
    <section ref={ref} className="py-32 relative overflow-hidden bg-gradient-to-b from-slate-900 via-black to-slate-950">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900 via-black to-slate-950"></div>
        {/* Ethereal glow effects */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-amber-400/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-slate-400/6 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Elite Section Header */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-20"
        >
          <motion.div variants={itemVariants} className="mb-6">
            <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl">
              <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
                Elite Testimonials
              </span>
            </div>
          </motion.div>

          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-6xl font-black mb-6 tracking-tight"
          >
            <span className="block text-white mb-2">
              Trusted by Digital
            </span>
            <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
              Aristocracy
            </span>
          </motion.h2>

          <motion.p
            variants={itemVariants}
            className="text-xl text-slate-400 max-w-3xl mx-auto font-light leading-relaxed"
          >
            Discover how the world's most prestigious enterprises elevate their operations with IMPERIUM.
          </motion.p>
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
            >
              <div className="relative p-8 bg-black/60 backdrop-blur-xl rounded-3xl border border-slate-700/50 hover:border-amber-400/30 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-amber-400/10">
                {/* Elite Quote Icon */}
                <div className="absolute top-6 right-6 opacity-20">
                  <Quote className="w-8 h-8 text-amber-400" />
                </div>

                {/* Elite Rating */}
                <div className="flex items-center space-x-1 mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-amber-400 fill-current" />
                  ))}
                </div>

                {/* Elite Content */}
                <p className="text-slate-300 leading-relaxed mb-8 relative z-10 font-light">
                  "{testimonial.content}"
                </p>

                {/* Elite Author */}
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-amber-400/20"
                    />
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-r ${testimonial.gradient} opacity-0 group-hover:opacity-30 transition-opacity duration-500`}></div>
                  </div>
                  <div>
                    <div className="text-white font-semibold tracking-wide">{testimonial.name}</div>
                    <div className="text-slate-400 text-sm font-light">
                      {testimonial.role} • {testimonial.company}
                    </div>
                  </div>
                </div>

                {/* Elite Hover Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${testimonial.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${testimonial.gradient} opacity-0 group-hover:opacity-10 blur-xl transition-opacity duration-500 -z-10`}></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Elite Bottom Stats */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="mt-20 text-center"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {[
              { number: "1,000+", label: "Elite Enterprises" },
              { number: "5.0/5", label: "Platinum Rating" },
              { number: "100%", label: "Satisfaction Guarantee" },
              { number: "24/7", label: "Concierge Available" },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent mb-2">
                  {stat.number}
                </div>
                <div className="text-slate-400 text-sm font-light">{stat.label}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
