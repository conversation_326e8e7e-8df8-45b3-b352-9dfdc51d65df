import { forwardRef, HTMLAttributes } from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

export interface StepCardProps extends HTMLAttributes<HTMLDivElement> {
  step: number
  title: string
  description?: string
  icon?: React.ReactNode
  variant?: 'default' | 'glass' | 'solid'
  size?: 'sm' | 'md' | 'lg'
  active?: boolean
  completed?: boolean
}

const variantStyles = {
  default: 'bg-white border border-neutral-200 shadow-sm',
  glass: 'bg-white/20 backdrop-blur-md border border-white/30 shadow-lg',
  solid: 'bg-neutral-900 text-white border border-neutral-800',
}

const sizeStyles = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
}

export const StepCard = forwardRef<HTMLDivElement, StepCardProps>(
  ({ 
    className,
    step,
    title,
    description,
    icon,
    variant = 'glass',
    size = 'md',
    active = false,
    completed = false,
    ...props 
  }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn(
          // Base styles
          'rounded-2xl transition-all duration-300',
          
          // Variant styles
          variantStyles[variant],
          
          // Size styles
          sizeStyles[size],
          
          // State styles
          active && 'ring-2 ring-blue-500 ring-offset-2',
          completed && 'bg-green-50 border-green-200',
          
          className
        )}
        whileHover={{ y: -2, scale: 1.02 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        {...props}
      >
        <div className="flex items-center gap-4">
          {/* Step Number or Icon */}
          <div className={cn(
            'flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center font-bold',
            variant === 'glass' ? 'bg-white/30 text-white' : 'bg-neutral-100 text-neutral-700',
            completed && 'bg-green-500 text-white',
            active && 'bg-blue-500 text-white'
          )}>
            {icon || step}
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className={cn(
              'font-semibold text-lg',
              variant === 'glass' ? 'text-white' : 'text-neutral-900',
              variant === 'solid' && 'text-white'
            )}>
              {title}
            </h3>
            {description && (
              <p className={cn(
                'text-sm mt-1',
                variant === 'glass' ? 'text-white/80' : 'text-neutral-600',
                variant === 'solid' && 'text-neutral-300'
              )}>
                {description}
              </p>
            )}
          </div>
        </div>
      </motion.div>
    )
  }
)

StepCard.displayName = 'StepCard'

// Steps Container component
export interface StepsContainerProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  direction?: 'horizontal' | 'vertical'
  showConnectors?: boolean
}

export const StepsContainer = forwardRef<HTMLDivElement, StepsContainerProps>(
  ({ 
    className,
    children,
    direction = 'horizontal',
    showConnectors = true,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'relative',
          direction === 'horizontal' ? 'flex items-center gap-6' : 'flex flex-col gap-6',
          className
        )}
        {...props}
      >
        {children}
        
        {/* Connectors */}
        {showConnectors && direction === 'horizontal' && (
          <div className="absolute top-1/2 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent -z-10" />
        )}
      </div>
    )
  }
)

StepsContainer.displayName = 'StepsContainer'

// Process Flow component (like in the image you showed)
export interface ProcessFlowProps extends HTMLAttributes<HTMLDivElement> {
  steps: Array<{
    title: string
    description?: string
    icon?: React.ReactNode
  }>
  variant?: 'default' | 'glass' | 'solid'
  title?: string
  subtitle?: string
}

export const ProcessFlow = forwardRef<HTMLDivElement, ProcessFlowProps>(
  ({ 
    className,
    steps,
    variant = 'glass',
    title,
    subtitle,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('text-center', className)}
        {...props}
      >
        {/* Header */}
        {title && (
          <div className="mb-12">
            <h2 className={cn(
              'text-3xl md:text-4xl font-bold mb-4',
              variant === 'glass' ? 'text-white' : 'text-neutral-900'
            )}>
              {title}
            </h2>
            {subtitle && (
              <p className={cn(
                'text-lg',
                variant === 'glass' ? 'text-white/80' : 'text-neutral-600'
              )}>
                {subtitle}
              </p>
            )}
          </div>
        )}
        
        {/* Steps */}
        <StepsContainer direction="horizontal" showConnectors>
          {steps.map((step, index) => (
            <StepCard
              key={index}
              step={index + 1}
              title={step.title}
              description={step.description}
              icon={step.icon}
              variant={variant}
              className="flex-1 max-w-xs"
            />
          ))}
        </StepsContainer>
      </div>
    )
  }
)

ProcessFlow.displayName = 'ProcessFlow'
