import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useState } from 'react'
import {
  Database,
  Mail,
  Slack,
  FileText,
  ShoppingCart,
  Users,
  BarChart3,
  ArrowRight,
  Play,
  Pause,
  CheckCircle
} from 'lucide-react'

export function AutomationShowcase() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const [activeAutomation, setActiveAutomation] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  const automations = [
    {
      id: 'elite-acquisition',
      title: 'Elite Client Acquisition',
      description: 'Capture, qualify and distribute high-value prospects to your elite teams with precision.',
      steps: [
        { icon: FileText, label: 'Elite Portal', color: 'from-amber-400 to-amber-500' },
        { icon: Database, label: 'CRM Validation', color: 'from-slate-400 to-slate-500' },
        { icon: Mail, label: 'Welcome Sequence', color: 'from-amber-500 to-amber-600' },
        { icon: Slack, label: 'Team Alert', color: 'from-slate-500 to-slate-600' },
      ],
      metrics: { processed: '3,247', conversion: '67%', time_saved: '35h/week' }
    },
    {
      id: 'empire-sync',
      title: 'Empire Commerce Sync',
      description: 'Synchronize orders, inventory, and client data across your digital empire seamlessly.',
      steps: [
        { icon: ShoppingCart, label: 'New Order', color: 'from-amber-400 to-amber-500' },
        { icon: Database, label: 'Inventory Update', color: 'from-slate-400 to-slate-500' },
        { icon: Mail, label: 'Client Confirmation', color: 'from-amber-500 to-amber-600' },
        { icon: BarChart3, label: 'Intelligence', color: 'from-slate-500 to-slate-600' },
      ],
      metrics: { processed: '2,134', conversion: '94%', time_saved: '45h/week' }
    },
    {
      id: 'aristocrat-onboarding',
      title: 'Aristocrat Onboarding',
      description: 'Guide new elite clients through a personalized and sophisticated journey.',
      steps: [
        { icon: Users, label: 'New Aristocrat', color: 'from-amber-400 to-amber-500' },
        { icon: Mail, label: 'Elite Sequence', color: 'from-slate-400 to-slate-500' },
        { icon: FileText, label: 'Bespoke Docs', color: 'from-amber-500 to-amber-600' },
        { icon: CheckCircle, label: 'Validation', color: 'from-slate-500 to-slate-600' },
      ],
      metrics: { processed: '867', conversion: '98%', time_saved: '60h/week' }
    },
  ]

  const currentAutomation = automations[activeAutomation]

  return (
    <section id="portfolio" ref={ref} className="py-32 relative overflow-hidden bg-gradient-to-b from-slate-950 via-black to-slate-900">
      {/* Elite Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-amber-400/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-slate-400/6 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Elite Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-6 py-3 bg-black/40 backdrop-blur-xl border border-amber-400/30 rounded-full text-sm font-medium text-amber-100 shadow-2xl mb-6">
            <div className="w-2 h-2 bg-amber-400 rounded-full mr-3 animate-pulse shadow-lg shadow-amber-400/50"></div>
            <span className="bg-gradient-to-r from-amber-200 to-amber-400 bg-clip-text text-transparent font-semibold">
              Elite Orchestrations
            </span>
          </div>

          <h2 className="text-4xl md:text-6xl font-black mb-6 tracking-tight">
            <span className="block text-white mb-2">
              Witness Digital
            </span>
            <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
              Mastery
            </span>
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Automation Selector */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="space-y-4">
              {automations.map((automation, index) => (
                <div
                  key={automation.id}
                  className={`p-6 rounded-2xl border cursor-pointer transition-all duration-500 ${
                    activeAutomation === index
                      ? 'bg-gradient-to-r from-amber-400/15 to-amber-500/15 border-amber-400/50 shadow-2xl shadow-amber-400/10'
                      : 'bg-black/40 backdrop-blur-xl border-slate-700/50 hover:border-slate-600/50'
                  }`}
                  onClick={() => setActiveAutomation(index)}
                >
                  <h3 className="text-xl font-bold text-white mb-2 tracking-wide">
                    {automation.title}
                  </h3>
                  <p className="text-slate-400 text-sm mb-4 font-light">
                    {automation.description}
                  </p>

                  {/* Elite Metrics */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-amber-400">
                        {automation.metrics.processed}
                      </div>
                      <div className="text-xs text-slate-500 font-light">Processed</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-amber-400">
                        {automation.metrics.conversion}
                      </div>
                      <div className="text-xs text-slate-500 font-light">Success</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-slate-400">
                        {automation.metrics.time_saved}
                      </div>
                      <div className="text-xs text-slate-500 font-light">Saved</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Workflow Visualization */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="bg-black/60 backdrop-blur-xl rounded-3xl border border-slate-700/50 p-8 shadow-2xl">
              {/* Elite Controls */}
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-xl font-bold text-white tracking-wide">
                  {currentAutomation.title}
                </h3>
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-500 hover:from-amber-500 hover:to-amber-600 rounded-full text-black transition-all duration-300 shadow-lg shadow-amber-400/25"
                >
                  {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                </button>
              </div>

              {/* Workflow Steps */}
              <div className="space-y-6">
                {currentAutomation.steps.map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.2 }}
                    className="flex items-center space-x-4"
                  >
                    {/* Step Icon */}
                    <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-xl flex items-center justify-center`}>
                      <step.icon className="w-6 h-6 text-white" />
                    </div>

                    {/* Elite Step Content */}
                    <div className="flex-1">
                      <div className="text-white font-medium tracking-wide">{step.label}</div>
                      <div className="w-full bg-slate-800 rounded-full h-2 mt-2">
                        <motion.div
                          className={`h-2 bg-gradient-to-r ${step.color} rounded-full shadow-sm`}
                          initial={{ width: 0 }}
                          animate={isPlaying ? { width: '100%' } : { width: 0 }}
                          transition={{
                            duration: 2,
                            delay: index * 0.5,
                            repeat: isPlaying ? Infinity : 0,
                            repeatDelay: 2
                          }}
                        />
                      </div>
                    </div>

                    {/* Elite Arrow */}
                    {index < currentAutomation.steps.length - 1 && (
                      <ArrowRight className="w-5 h-5 text-slate-400" />
                    )}
                  </motion.div>
                ))}
              </div>

              {/* Elite Status */}
              <div className="mt-8 p-4 bg-amber-400/10 border border-amber-400/30 rounded-xl">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-amber-400" />
                  <span className="text-amber-400 font-medium">
                    Elite orchestration active - {currentAutomation.metrics.processed} elements processed
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
