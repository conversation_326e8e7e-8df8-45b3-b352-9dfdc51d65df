import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { useState } from 'react'
import { 
  Database, 
  Mail, 
  Slack, 
  FileText, 
  ShoppingCart, 
  Users, 
  BarChart3,
  ArrowRight,
  Play,
  Pause,
  CheckCircle
} from 'lucide-react'

export function AutomationShowcase() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const [activeAutomation, setActiveAutomation] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  const automations = [
    {
      id: 'lead-processing',
      title: 'Traitement automatique des leads',
      description: 'Capturez, qualifiez et distribuez automatiquement vos leads aux bonnes équipes.',
      steps: [
        { icon: FileText, label: 'Formulaire web', color: 'from-blue-500 to-cyan-500' },
        { icon: Database, label: 'Validation CRM', color: 'from-green-500 to-emerald-500' },
        { icon: Mail, label: 'Email de bienvenue', color: 'from-purple-500 to-violet-500' },
        { icon: Slack, label: 'Notification équipe', color: 'from-yellow-500 to-orange-500' },
      ],
      metrics: { processed: '2,847', conversion: '34%', time_saved: '15h/semaine' }
    },
    {
      id: 'ecommerce-sync',
      title: 'Synchronisation e-commerce',
      description: 'Synchronisez automatiquement vos commandes, stocks et données clients.',
      steps: [
        { icon: ShoppingCart, label: 'Nouvelle commande', color: 'from-pink-500 to-rose-500' },
        { icon: Database, label: 'Mise à jour stock', color: 'from-indigo-500 to-purple-500' },
        { icon: Mail, label: 'Confirmation client', color: 'from-green-500 to-teal-500' },
        { icon: BarChart3, label: 'Analytics', color: 'from-orange-500 to-red-500' },
      ],
      metrics: { processed: '1,234', conversion: '89%', time_saved: '25h/semaine' }
    },
    {
      id: 'customer-onboarding',
      title: 'Onboarding client automatisé',
      description: 'Guidez vos nouveaux clients avec un parcours personnalisé et automatisé.',
      steps: [
        { icon: Users, label: 'Nouveau client', color: 'from-cyan-500 to-blue-500' },
        { icon: Mail, label: 'Séquence emails', color: 'from-violet-500 to-purple-500' },
        { icon: FileText, label: 'Documents', color: 'from-emerald-500 to-green-500' },
        { icon: CheckCircle, label: 'Validation', color: 'from-teal-500 to-cyan-500' },
      ],
      metrics: { processed: '567', conversion: '92%', time_saved: '40h/semaine' }
    },
  ]

  const currentAutomation = automations[activeAutomation]

  return (
    <section id="automations" ref={ref} className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full border border-purple-500/30 backdrop-blur-sm text-purple-200 text-sm font-medium mb-6">
            Automatisations en action
          </span>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Voyez la magie
            </span>
            <br />
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              en action
            </span>
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Automation Selector */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="space-y-4">
              {automations.map((automation, index) => (
                <div
                  key={automation.id}
                  className={`p-6 rounded-2xl border cursor-pointer transition-all duration-300 ${
                    activeAutomation === index
                      ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/50'
                      : 'bg-slate-800/30 border-slate-700/50 hover:border-slate-600/50'
                  }`}
                  onClick={() => setActiveAutomation(index)}
                >
                  <h3 className="text-xl font-bold text-white mb-2">
                    {automation.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-4">
                    {automation.description}
                  </p>
                  
                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-purple-400">
                        {automation.metrics.processed}
                      </div>
                      <div className="text-xs text-gray-500">Traités</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-400">
                        {automation.metrics.conversion}
                      </div>
                      <div className="text-xs text-gray-500">Succès</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-blue-400">
                        {automation.metrics.time_saved}
                      </div>
                      <div className="text-xs text-gray-500">Économisées</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Workflow Visualization */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-3xl border border-slate-700/50 p-8">
              {/* Controls */}
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-xl font-bold text-white">
                  {currentAutomation.title}
                </h3>
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
                >
                  {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                </button>
              </div>

              {/* Workflow Steps */}
              <div className="space-y-6">
                {currentAutomation.steps.map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.2 }}
                    className="flex items-center space-x-4"
                  >
                    {/* Step Icon */}
                    <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-xl flex items-center justify-center`}>
                      <step.icon className="w-6 h-6 text-white" />
                    </div>

                    {/* Step Content */}
                    <div className="flex-1">
                      <div className="text-white font-medium">{step.label}</div>
                      <div className="w-full bg-slate-700 rounded-full h-2 mt-2">
                        <motion.div
                          className={`h-2 bg-gradient-to-r ${step.color} rounded-full`}
                          initial={{ width: 0 }}
                          animate={isPlaying ? { width: '100%' } : { width: 0 }}
                          transition={{ 
                            duration: 2, 
                            delay: index * 0.5,
                            repeat: isPlaying ? Infinity : 0,
                            repeatDelay: 2
                          }}
                        />
                      </div>
                    </div>

                    {/* Arrow */}
                    {index < currentAutomation.steps.length - 1 && (
                      <ArrowRight className="w-5 h-5 text-gray-400" />
                    )}
                  </motion.div>
                ))}
              </div>

              {/* Status */}
              <div className="mt-8 p-4 bg-green-500/10 border border-green-500/30 rounded-xl">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-medium">
                    Automatisation active - {currentAutomation.metrics.processed} éléments traités
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
