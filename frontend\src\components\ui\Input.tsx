import { forwardRef, InputHTMLAttributes, useState } from 'react'
import { motion } from 'framer-motion'
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'
import { cn } from '../../lib/utils'

// Input variants
const inputVariants = {
  // Size variants
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-3 text-sm',
  lg: 'h-12 px-4 text-base',
  
  // State variants
  default: 'border-neutral-300 focus:border-brand-500 focus:ring-brand-500',
  error: 'border-error-300 focus:border-error-500 focus:ring-error-500',
  success: 'border-success-300 focus:border-success-500 focus:ring-success-500',
}

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  size?: 'sm' | 'md' | 'lg'
  state?: 'default' | 'error' | 'success'
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  label?: string
  description?: string
  errorMessage?: string
  successMessage?: string
  showPasswordToggle?: boolean
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className,
    type = 'text',
    size = 'md',
    state = 'default',
    leftIcon,
    rightIcon,
    label,
    description,
    errorMessage,
    successMessage,
    showPasswordToggle = false,
    disabled,
    id,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)
    
    // Generate unique ID if not provided
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
    const descriptionId = description ? `${inputId}-description` : undefined
    const errorId = errorMessage ? `${inputId}-error` : undefined
    const successId = successMessage ? `${inputId}-success` : undefined
    
    // Determine actual input type
    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password')
      : type
    
    // Determine state based on props
    const currentState = errorMessage ? 'error' : successMessage ? 'success' : state
    
    const baseInputClasses = cn(
      // Base styles
      'flex w-full rounded-xl border bg-white transition-all duration-200',
      'placeholder:text-neutral-500',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white',
      'disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-neutral-50',
      
      // Size styles
      inputVariants[size],
      
      // State styles
      inputVariants[currentState],
      
      // Icon padding adjustments
      leftIcon && 'pl-10',
      (rightIcon || showPasswordToggle) && 'pr-10',
      
      className
    )

    return (
      <div className="w-full">
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-neutral-700 mb-2"
          >
            {label}
          </label>
        )}
        
        {/* Description */}
        {description && (
          <p 
            id={descriptionId}
            className="text-sm text-neutral-600 mb-2"
          >
            {description}
          </p>
        )}
        
        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-neutral-400 text-sm">
                {leftIcon}
              </span>
            </div>
          )}
          
          {/* Input */}
          <motion.input
            ref={ref}
            type={inputType}
            id={inputId}
            className={baseInputClasses}
            disabled={disabled}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            aria-describedby={cn(
              descriptionId,
              errorId,
              successId
            )}
            aria-invalid={currentState === 'error'}
            whileFocus={{ scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            {...props}
          />
          
          {/* Right Icon or Password Toggle */}
          {(rightIcon || showPasswordToggle) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {showPasswordToggle && type === 'password' ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:text-neutral-600 transition-colors"
                  aria-label={showPassword ? 'Masquer le mot de passe' : 'Afficher le mot de passe'}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              ) : rightIcon ? (
                <span className="text-neutral-400 text-sm">
                  {rightIcon}
                </span>
              ) : null}
            </div>
          )}
          
          {/* State Icons */}
          {currentState === 'error' && !rightIcon && !showPasswordToggle && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <AlertCircle className="w-4 h-4 text-error-500" />
            </div>
          )}
          
          {currentState === 'success' && !rightIcon && !showPasswordToggle && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <CheckCircle className="w-4 h-4 text-success-500" />
            </div>
          )}
        </div>
        
        {/* Error Message */}
        {errorMessage && (
          <motion.p
            id={errorId}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 text-sm text-error-600 flex items-center gap-1"
            role="alert"
          >
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            {errorMessage}
          </motion.p>
        )}
        
        {/* Success Message */}
        {successMessage && (
          <motion.p
            id={successId}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 text-sm text-success-600 flex items-center gap-1"
          >
            <CheckCircle className="w-4 h-4 flex-shrink-0" />
            {successMessage}
          </motion.p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'
