import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Calendar, FileText, CheckCircle } from 'lucide-react'
import { ProcessFlow } from '../ui/StepCard'

export function ProcessSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const steps = [
    {
      title: "Book a Call",
      description: "Planifiez une consultation",
      icon: <Calendar className="w-6 h-6" />,
    },
    {
      title: "Create Tickets",
      description: "Définissez vos besoins",
      icon: <FileText className="w-6 h-6" />,
    },
    {
      title: "We Deliver",
      description: "Recevez votre solution",
      icon: <CheckCircle className="w-6 h-6" />,
    },
  ]

  return (
    <section 
      ref={ref} 
      className="relative py-32 bg-gradient-to-br from-neutral-100 via-neutral-50 to-white overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Subtle geometric shapes */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/30 to-purple-100/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-emerald-100/20 to-blue-100/20 rounded-full blur-3xl"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6 tracking-tight">
              Let's{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                &lt;Build&gt;
              </span>{' '}
              What Actually Works
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Un processus simple et efficace pour transformer vos idées en automatisations puissantes
            </p>
          </motion.div>

          {/* Process Flow */}
          <motion.div variants={itemVariants}>
            <ProcessFlow
              steps={steps}
              variant="default"
              className="max-w-4xl mx-auto"
            />
          </motion.div>

          {/* Bottom CTA */}
          <motion.div variants={itemVariants} className="text-center mt-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-white rounded-2xl shadow-lg border border-neutral-200">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-neutral-700 font-medium">
                Prêt à commencer ? Réservez votre appel gratuit
              </span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
