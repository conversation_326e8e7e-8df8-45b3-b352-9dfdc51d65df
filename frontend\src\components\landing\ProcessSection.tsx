import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Calendar, FileText, CheckCircle } from 'lucide-react'
import { ProcessFlow } from '../ui/StepCard'

export function ProcessSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const steps = [
    {
      title: "Request Audience",
      description: "Schedule elite consultation",
      icon: <Calendar className="w-6 h-6" />,
    },
    {
      title: "Define Vision",
      description: "Articulate your empire's needs",
      icon: <FileText className="w-6 h-6" />,
    },
    {
      title: "We Orchestrate",
      description: "Receive your bespoke solution",
      icon: <CheckCircle className="w-6 h-6" />,
    },
  ]

  return (
    <section
      ref={ref}
      className="relative py-32 bg-gradient-to-br from-slate-900 via-black to-slate-950 overflow-hidden"
    >
      {/* Elite Background Elements */}
      <div className="absolute inset-0">
        {/* Elite geometric shapes */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-amber-400/10 to-amber-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-slate-400/8 to-slate-500/8 rounded-full blur-3xl"></div>

        {/* Elite Grid pattern */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F59E0B' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Elite Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-white mb-6 tracking-tight">
              Let's{' '}
              <span className="bg-gradient-to-r from-amber-300 via-amber-400 to-amber-500 bg-clip-text text-transparent">
                &lt;Orchestrate&gt;
              </span>{' '}
              Digital Excellence
            </h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto font-light leading-relaxed">
              An elegant and sophisticated process to transform your vision into powerful automation empires
            </p>
          </motion.div>

          {/* Process Flow */}
          <motion.div variants={itemVariants}>
            <ProcessFlow
              steps={steps}
              variant="default"
              className="max-w-4xl mx-auto"
            />
          </motion.div>

          {/* Elite Bottom CTA */}
          <motion.div variants={itemVariants} className="text-center mt-16">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-black/40 backdrop-blur-xl rounded-2xl shadow-2xl border border-amber-400/30">
              <div className="w-3 h-3 bg-amber-400 rounded-full animate-pulse shadow-lg shadow-amber-400/50"></div>
              <span className="text-slate-300 font-medium">
                Ready to ascend? Request your complimentary audience
              </span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
