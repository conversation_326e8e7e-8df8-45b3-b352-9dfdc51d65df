// SCRAPER ARTHUR LOYD - Sites spécialisés immobilier d'entreprise
const http = require('http');
const https = require('https');

const PORT = 3004;

// Critères de recherche
const SEARCH_CRITERIA = {
  minPrice: 800000,
  maxPrice: 1400000,
  minBuildingArea: 400,
  cities: ['Lyon', 'Bordeaux'],
  propertyTypes: ['entrepot', 'entrepôt', 'warehouse', 'local industriel', 'hangar', 'stockage']
};

// Base de données
let allProperties = [];
let scrapingStatus = { isRunning: false, currentStep: '', progress: 0 };

// Sites spécialisés immobilier d'entreprise (les vrais !)
const BUSINESS_REAL_ESTATE_SITES = {
  arthur_loyd_lyon: {
    name: '<PERSON>',
    baseUrl: 'https://www.arthur-loyd-lyon.com',
    searchUrls: [
      'https://www.arthur-loyd-lyon.com/locaux-activite-entrepots-vente/',
      'https://www.arthur-loyd-lyon.com/recherche/?type=vente&usage=entrepot',
      'https://www.arthur-loyd-lyon.com/recherche/?type=vente&usage=local-industriel'
    ]
  },
  arthur_loyd_bordeaux: {
    name: 'Arthur Loyd Bordeaux',
    baseUrl: 'https://www.arthur-loyd-bordeaux.com',
    searchUrls: [
      'https://www.arthur-loyd-bordeaux.com/locaux-activite-entrepots-vente/',
      'https://www.arthur-loyd-bordeaux.com/recherche/?type=vente&usage=entrepot'
    ]
  },
  cbre: {
    name: 'CBRE France',
    baseUrl: 'https://www.cbre.fr',
    searchUrls: [
      'https://www.cbre.fr/fr-fr/properties/industrial',
      'https://www.cbre.fr/fr-fr/properties?location=lyon&type=industrial',
      'https://www.cbre.fr/fr-fr/properties?location=bordeaux&type=industrial'
    ]
  },
  jll: {
    name: 'JLL France',
    baseUrl: 'https://www.jll.fr',
    searchUrls: [
      'https://www.jll.fr/fr/immobilier-entreprise/entrepots-logistique',
      'https://www.jll.fr/fr/immobilier-entreprise/locaux-industriels'
    ]
  },
  cushman: {
    name: 'Cushman & Wakefield',
    baseUrl: 'https://www.cushmanwakefield.fr',
    searchUrls: [
      'https://www.cushmanwakefield.fr/fr-fr/france/properties/industrial',
      'https://www.cushmanwakefield.fr/fr-fr/france/properties/logistics'
    ]
  }
};

// Fonction HTTP robuste
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;

    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'no-cache',
        'Referer': 'https://www.google.com/',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site'
      },
      timeout: 20000
    };

    const req = client.get(url, options, (res) => {
      let data = '';

      // Gérer la compression gzip
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib');
        const gunzip = zlib.createGunzip();
        res.pipe(gunzip);
        gunzip.on('data', chunk => data += chunk);
        gunzip.on('end', () => resolve(data));
        gunzip.on('error', reject);
      } else {
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      }
    });

    req.on('error', reject);
    req.setTimeout(20000, () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

// Extraction spécialisée pour Arthur Loyd
function extractArthurLoydProperties(html, city, siteName) {
  const properties = [];

  console.log(`🏢 Analyse Arthur Loyd pour ${city}...`);

  // Patterns spécifiques Arthur Loyd
  const propertyBlocks = html.match(/<article[^>]*class="[^"]*bien[^"]*"[^>]*>.*?<\/article>/gs) ||
                         html.match(/<div[^>]*class="[^"]*property[^"]*"[^>]*>.*?<\/div>/gs) ||
                         html.match(/<div[^>]*class="[^"]*annonce[^"]*"[^>]*>.*?<\/div>/gs) ||
                         [];

  console.log(`📋 ${propertyBlocks.length} blocs de propriétés trouvés`);

  // Si pas de blocs spécifiques, analyser tout le HTML
  if (propertyBlocks.length === 0) {
    propertyBlocks.push(html);
  }

  for (let i = 0; i < propertyBlocks.length; i++) {
    const block = propertyBlocks[i];

    // Extraire le prix
    let price = 0;
    const pricePatterns = [
      /(\d{3,}(?:\s?\d{3})*)\s*€/g,
      /(\d{3,}(?:\s?\d{3})*)\s*euros?/gi,
      /prix[^0-9]*(\d{3,}(?:\s?\d{3})*)/gi,
      /€\s*(\d{3,}(?:\s?\d{3})*)/g
    ];

    for (const pattern of pricePatterns) {
      let match;
      while ((match = pattern.exec(block)) !== null) {
        const priceStr = match[1].replace(/\s/g, '');
        const priceNum = parseInt(priceStr);
        if (priceNum >= 100000 && priceNum <= 10000000) {
          price = priceNum;
          break;
        }
      }
      if (price > 0) break;
    }

    // Extraire la superficie
    let buildingArea = 0;
    const areaPatterns = [
      /(\d+(?:[,.]?\d+)?)\s*m[²2]/gi,
      /(\d+(?:[,.]?\d+)?)\s*m²/gi,
      /surface[^0-9]*(\d+(?:[,.]?\d+)?)/gi,
      /superficie[^0-9]*(\d+(?:[,.]?\d+)?)/gi
    ];

    for (const pattern of areaPatterns) {
      let match;
      while ((match = pattern.exec(block)) !== null) {
        const areaStr = match[1].replace(',', '.');
        const areaNum = parseFloat(areaStr);
        if (areaNum >= 50 && areaNum <= 50000) {
          buildingArea = areaNum;
          break;
        }
      }
      if (buildingArea > 0) break;
    }

    // Extraire le titre
    let title = '';
    const titlePatterns = [
      /<h[1-6][^>]*>([^<]+)<\/h[1-6]>/gi,
      /title="([^"]+)"/gi,
      /alt="([^"]+)"/gi,
      /<a[^>]*>([^<]*(?:entrepot|local|industriel|stockage)[^<]*)<\/a>/gi
    ];

    for (const pattern of titlePatterns) {
      const match = block.match(pattern);
      if (match) {
        title = match[1].trim();
        if (title.length > 10) break;
      }
    }

    // Extraire l'URL de l'annonce
    let propertyUrl = '';
    const urlMatch = block.match(/href="([^"]*(?:ref-|annonce|bien)[^"]*)"/i);
    if (urlMatch) {
      propertyUrl = urlMatch[1];
      if (!propertyUrl.startsWith('http')) {
        propertyUrl = BUSINESS_REAL_ESTATE_SITES[siteName]?.baseUrl + propertyUrl;
      }
    }

    // Vérifier si c'est un entrepôt/local industriel
    const isIndustrial = SEARCH_CRITERIA.propertyTypes.some(type =>
      (title + ' ' + block).toLowerCase().includes(type.toLowerCase())
    );

    // Calculer le score
    let score = 0;

    // Prix (40 points)
    if (price >= SEARCH_CRITERIA.minPrice && price <= SEARCH_CRITERIA.maxPrice) {
      score += 40;
    } else if (price > 0 && price >= SEARCH_CRITERIA.minPrice * 0.7) {
      score += 25;
    }

    // Superficie (25 points)
    if (buildingArea >= SEARCH_CRITERIA.minBuildingArea) {
      score += 25;
    } else if (buildingArea > 0) {
      score += 15;
    }

    // Type industriel (25 points)
    if (isIndustrial) {
      score += 25;
    }

    // Ville (10 points)
    if (SEARCH_CRITERIA.cities.some(c => city.toLowerCase().includes(c.toLowerCase()))) {
      score += 10;
    }

    // Créer la propriété si score suffisant
    if (score >= 25 && (price > 0 || buildingArea > 0 || isIndustrial)) {
      const property = {
        id: `${siteName}-${Date.now()}-${i}`,
        title: title || `Propriété industrielle ${city}`,
        description: `Propriété trouvée sur ${BUSINESS_REAL_ESTATE_SITES[siteName]?.name}`,
        price: price,
        city: city,
        buildingArea: buildingArea,
        landArea: buildingArea > 0 ? Math.floor(buildingArea * 2) : 0,
        source: siteName,
        sourceUrl: propertyUrl || `Recherche ${siteName}`,
        publishedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        scrapedAt: new Date(),
        isNew: true,
        isAvailable: true,
        matchScore: score
      };

      properties.push(property);
      console.log(`✅ Propriété ajoutée: ${property.title} - ${property.price}€ - ${property.buildingArea}m² - Score: ${score}`);
    }
  }

  console.log(`🎯 ${properties.length} propriétés extraites pour ${city}`);
  return properties;
}

// Fonction principale de scraping
async function runBusinessRealEstateScraping() {
  if (scrapingStatus.isRunning) {
    console.log('⚠️ Scraping déjà en cours...');
    return { success: false, message: 'Scraping déjà en cours' };
  }

  scrapingStatus.isRunning = true;
  scrapingStatus.currentStep = 'Initialisation...';
  scrapingStatus.progress = 0;

  console.log('🏢 Démarrage du scraping immobilier d\'entreprise...');
  allProperties = [];

  try {
    const sites = Object.keys(BUSINESS_REAL_ESTATE_SITES);
    const totalSites = sites.length;

    for (let i = 0; i < sites.length; i++) {
      const siteName = sites[i];
      const siteConfig = BUSINESS_REAL_ESTATE_SITES[siteName];

      scrapingStatus.progress = (i / totalSites) * 100;
      scrapingStatus.currentStep = `Scraping ${siteConfig.name}...`;

      console.log(`\n🏢 === SCRAPING ${siteConfig.name} ===`);

      try {
        for (const searchUrl of siteConfig.searchUrls) {
          const city = searchUrl.includes('lyon') || siteName.includes('lyon') ? 'Lyon' : 'Bordeaux';

          console.log(`📍 Recherche ${city}: ${searchUrl}`);

          try {
            const html = await fetchPage(searchUrl);

            if (html.length > 1000) {
              const siteProperties = extractArthurLoydProperties(html, city, siteName);
              allProperties.push(...siteProperties);

              console.log(`✅ ${siteProperties.length} propriétés ajoutées pour ${city}`);
            } else {
              console.log(`⚠️ Contenu insuffisant pour ${city} (${html.length} caractères)`);
            }

          } catch (error) {
            console.log(`❌ Erreur ${city}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

      } catch (error) {
        console.log(`❌ Erreur ${siteConfig.name}: ${error.message}`);
      }

      // Pause entre les sites
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Filtrer et trier
    allProperties = allProperties
      .filter(prop => prop.matchScore >= 25)
      .sort((a, b) => b.matchScore - a.matchScore);

    // Supprimer les doublons
    const uniqueProperties = [];
    const seen = new Set();

    for (const prop of allProperties) {
      const key = `${prop.price}-${prop.buildingArea}-${prop.city}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueProperties.push(prop);
      }
    }

    allProperties = uniqueProperties;

    scrapingStatus.currentStep = 'Terminé !';
    scrapingStatus.progress = 100;

    console.log(`\n✅ Scraping immobilier d'entreprise terminé: ${allProperties.length} propriétés trouvées`);

    return {
      success: true,
      propertiesFound: allProperties.length,
      sitesScraped: sites.length,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Erreur scraping:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    };
  } finally {
    scrapingStatus.isRunning = false;
    setTimeout(() => {
      scrapingStatus.progress = 0;
    }, 3000);
  }
}

// Interface web
function generateHTML() {
  const stats = {
    total: allProperties.length,
    avgPrice: allProperties.length > 0 ?
      Math.round(allProperties.reduce((sum, p) => sum + p.price, 0) / allProperties.length) : 0,
    avgScore: allProperties.length > 0 ?
      Math.round(allProperties.reduce((sum, p) => sum + p.matchScore, 0) / allProperties.length) : 0,
    sources: [...new Set(allProperties.map(p => p.source))].length
  };

  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 Arthur Loyd Scraper - Immobilier d'Entreprise</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; color: #334155; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1e40af, #3b82f6); color: white; padding: 30px; text-align: center; margin-bottom: 30px; border-radius: 12px; }
        .status-badge { background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; margin-top: 15px; display: inline-block; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #1e40af; }
        .property-card { background: white; border-radius: 12px; padding: 24px; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #1e40af; }
        .property-title { font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 10px; }
        .property-price { font-size: 1.5rem; font-weight: bold; color: #1e40af; margin-bottom: 10px; }
        .property-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0; }
        .detail-item { display: flex; align-items: center; gap: 8px; font-size: 0.9rem; }
        .btn { padding: 12px 24px; border: none; border-radius: 6px; font-weight: 500; cursor: pointer; transition: all 0.2s; margin: 5px; text-decoration: none; display: inline-block; }
        .btn-primary { background: #1e40af; color: white; }
        .btn-primary:hover { background: #1d4ed8; }
        .btn-primary:disabled { background: #9ca3af; cursor: not-allowed; }
        .scraping-control { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; text-align: center; }
        .progress-section { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; display: ${scrapingStatus.isRunning ? 'block' : 'none'}; }
        .progress-bar { width: 100%; height: 20px; background: #e2e8f0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #1e40af, #3b82f6); transition: width 0.3s ease; width: ${scrapingStatus.progress}%; }
        .badge { padding: 4px 12px; border-radius: 20px; font-size: 0.75rem; font-weight: 500; }
        .badge-success { background: #dcfce7; color: #166534; }
        .badge-warning { background: #fef3c7; color: #92400e; }
        .badge-info { background: #dbeafe; color: #1e40af; }
        .empty-state { text-align: center; padding: 60px 20px; color: #64748b; }
        .sites-list { display: flex; gap: 10px; flex-wrap: wrap; justify-content: center; margin-top: 20px; }
        .site-badge { background: rgba(255,255,255,0.2); padding: 6px 12px; border-radius: 15px; font-size: 0.8rem; }
        .url-link { color: #1e40af; text-decoration: none; font-size: 0.9rem; }
        .url-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Arthur Loyd Scraper - Immobilier d'Entreprise</h1>
            <p>Scraping des VRAIS sites d'immobilier industriel</p>
            <div class="status-badge">
                🎯 Sites spécialisés • 🏢 Immobilier d'entreprise • 📊 Extraction professionnelle
            </div>
            <div class="sites-list">
                <span class="site-badge">Arthur Loyd Lyon</span>
                <span class="site-badge">Arthur Loyd Bordeaux</span>
                <span class="site-badge">CBRE France</span>
                <span class="site-badge">JLL France</span>
                <span class="site-badge">Cushman & Wakefield</span>
            </div>
        </div>

        <div class="scraping-control">
            <h3>🚀 Scraping Immobilier d'Entreprise</h3>
            <p>Analyse des sites spécialisés en immobilier industriel et d'entreprise</p>
            <button class="btn btn-primary" onclick="startScraping()" ${scrapingStatus.isRunning ? 'disabled' : ''}>
                ${scrapingStatus.isRunning ? '⏳ Scraping en cours...' : '🏢 Démarrer le Scraping Arthur Loyd'}
            </button>
        </div>

        <div class="progress-section">
            <h3>📊 Progression</h3>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <p><strong>Étape:</strong> <span id="current-step">${scrapingStatus.currentStep}</span></p>
            <p><strong>Progression:</strong> <span id="progress">${Math.round(scrapingStatus.progress)}%</span></p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${stats.total}</div>
                <div>Propriétés trouvées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgPrice.toLocaleString()}€</div>
                <div>Prix moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgScore}%</div>
                <div>Score moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.sources}</div>
                <div>Sources actives</div>
            </div>
        </div>

        <div id="properties">
            ${allProperties.length === 0 ? `
                <div class="empty-state">
                    <h3>🏢 Recherche d'entrepôts industriels</h3>
                    <p>Lancez le scraping pour analyser Arthur Loyd et les sites spécialisés</p>
                    <p>Ces sites contiennent de vrais entrepôts et locaux industriels</p>
                </div>
            ` : allProperties.map(property => `
                <div class="property-card">
                    <div class="property-title">${property.title}</div>
                    <div class="property-price">${property.price > 0 ? property.price.toLocaleString() + '€' : 'Prix sur demande'}</div>
                    <div style="margin: 10px 0;">
                        <span class="badge badge-success">✅ Disponible</span>
                        <span class="badge badge-info">⭐ ${property.matchScore}% match</span>
                        ${property.buildingArea > 0 ? `<span class="badge badge-warning">📐 ${property.buildingArea}m²</span>` : ''}
                    </div>
                    <div class="property-details">
                        <div class="detail-item">📍 ${property.city}</div>
                        <div class="detail-item">🏢 ${BUSINESS_REAL_ESTATE_SITES[property.source]?.name || property.source}</div>
                        <div class="detail-item">🕒 ${new Date(property.scrapedAt).toLocaleString()}</div>
                        <div class="detail-item">
                            ${property.sourceUrl && property.sourceUrl !== 'Recherche ' + property.source ?
                                `<a href="${property.sourceUrl}" target="_blank" class="url-link">🔗 Voir l'annonce</a>` :
                                '📄 Page de recherche'
                            }
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    </div>

    <script>
        async function startScraping() {
            try {
                const response = await fetch('/api/scraping/business', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    setTimeout(() => location.reload(), 2000);
                } else {
                    alert('Erreur: ' + (result.error || result.message));
                }
            } catch (error) {
                alert('Erreur de connexion: ' + error.message);
            }
        }

        // Auto-refresh pendant le scraping
        if (${scrapingStatus.isRunning}) {
            setTimeout(() => location.reload(), 5000);
        }
    </script>
</body>
</html>`;
}

// Serveur HTTP
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(generateHTML());
  } else if (url.pathname === '/api/scraping/business' && req.method === 'POST') {
    const result = await runBusinessRealEstateScraping();
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(result));
  } else if (url.pathname === '/api/status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      isRunning: scrapingStatus.isRunning,
      currentStep: scrapingStatus.currentStep,
      progress: scrapingStatus.progress,
      propertiesCount: allProperties.length
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

// Démarrage
server.listen(PORT, () => {
  console.log(`🏢 Scraper ARTHUR LOYD démarré sur http://localhost:${PORT}`);
  console.log(`🎯 Sites spécialisés: Arthur Loyd, CBRE, JLL, Cushman & Wakefield`);
  console.log(`🏢 Focus immobilier d'entreprise et industriel`);
  console.log(`📊 Extraction adaptée aux sites professionnels`);
  console.log('✅ Prêt ! Ouvrez http://localhost:3004 pour scraper les VRAIS sites !');
});
