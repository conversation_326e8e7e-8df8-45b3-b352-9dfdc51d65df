import { forwardRef, HTMLAttributes } from 'react'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import { cn } from '../../lib/utils'

// Badge variants
const badgeVariants = {
  // Intent variants
  default: 'bg-neutral-100 text-neutral-800 border-neutral-200',
  primary: 'bg-brand-100 text-brand-800 border-brand-200',
  secondary: 'bg-neutral-100 text-neutral-600 border-neutral-200',
  success: 'bg-success-100 text-success-800 border-success-200',
  warning: 'bg-warning-100 text-warning-800 border-warning-200',
  error: 'bg-error-100 text-error-800 border-error-200',
  info: 'bg-info-100 text-info-800 border-info-200',
  
  // Style variants
  solid: '',
  outline: 'bg-transparent border',
  soft: 'border-transparent',
  
  // Size variants
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-0.5 text-sm',
  lg: 'px-3 py-1 text-sm',
}

export interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  style?: 'solid' | 'outline' | 'soft'
  size?: 'sm' | 'md' | 'lg'
  removable?: boolean
  onRemove?: () => void
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  children: React.ReactNode
}

export const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
  ({ 
    className,
    variant = 'default',
    style = 'soft',
    size = 'md',
    removable = false,
    onRemove,
    leftIcon,
    rightIcon,
    children,
    ...props 
  }, ref) => {
    const baseClasses = cn(
      // Base styles
      'inline-flex items-center gap-1 rounded-full font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      
      // Variant styles
      badgeVariants[variant],
      
      // Style variant
      style === 'outline' && badgeVariants.outline,
      style === 'soft' && badgeVariants.soft,
      
      // Size styles
      badgeVariants[size],
      
      className
    )

    return (
      <motion.span
        ref={ref}
        className={baseClasses}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.2 }}
        {...props}
      >
        {leftIcon && (
          <span className="flex-shrink-0">
            {leftIcon}
          </span>
        )}
        
        <span className="truncate">
          {children}
        </span>
        
        {rightIcon && !removable && (
          <span className="flex-shrink-0">
            {rightIcon}
          </span>
        )}
        
        {removable && (
          <button
            type="button"
            onClick={onRemove}
            className="flex-shrink-0 ml-0.5 h-4 w-4 rounded-full inline-flex items-center justify-center hover:bg-black/10 focus:outline-none focus:bg-black/10 transition-colors"
            aria-label="Supprimer"
          >
            <X className="w-3 h-3" />
          </button>
        )}
      </motion.span>
    )
  }
)

Badge.displayName = 'Badge'

// Status Badge - specialized badge for status indicators
export interface StatusBadgeProps extends Omit<BadgeProps, 'variant' | 'leftIcon'> {
  status: 'active' | 'inactive' | 'pending' | 'success' | 'error' | 'warning'
  showDot?: boolean
}

export const StatusBadge = forwardRef<HTMLSpanElement, StatusBadgeProps>(
  ({ status, showDot = true, children, ...props }, ref) => {
    const statusConfig = {
      active: { variant: 'success' as const, label: 'Actif', dotColor: 'bg-success-500' },
      inactive: { variant: 'secondary' as const, label: 'Inactif', dotColor: 'bg-neutral-400' },
      pending: { variant: 'warning' as const, label: 'En attente', dotColor: 'bg-warning-500' },
      success: { variant: 'success' as const, label: 'Succès', dotColor: 'bg-success-500' },
      error: { variant: 'error' as const, label: 'Erreur', dotColor: 'bg-error-500' },
      warning: { variant: 'warning' as const, label: 'Attention', dotColor: 'bg-warning-500' },
    }

    const config = statusConfig[status]

    return (
      <Badge
        ref={ref}
        variant={config.variant}
        leftIcon={showDot ? (
          <span className={cn('w-2 h-2 rounded-full', config.dotColor)} />
        ) : undefined}
        {...props}
      >
        {children || config.label}
      </Badge>
    )
  }
)

StatusBadge.displayName = 'StatusBadge'
