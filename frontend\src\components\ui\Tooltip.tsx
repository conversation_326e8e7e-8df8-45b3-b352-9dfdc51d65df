import { useState, useRef, cloneElement, ReactElement } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '../../lib/utils'

// Tooltip variants
const tooltipVariants = {
  // Position variants
  top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
  bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
  left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
  right: 'left-full top-1/2 transform -translate-y-1/2 ml-2',
  
  // Arrow variants
  arrowTop: 'after:absolute after:top-full after:left-1/2 after:transform after:-translate-x-1/2 after:border-4 after:border-transparent after:border-t-neutral-900',
  arrowBottom: 'after:absolute after:bottom-full after:left-1/2 after:transform after:-translate-x-1/2 after:border-4 after:border-transparent after:border-b-neutral-900',
  arrowLeft: 'after:absolute after:left-full after:top-1/2 after:transform after:-translate-y-1/2 after:border-4 after:border-transparent after:border-l-neutral-900',
  arrowRight: 'after:absolute after:right-full after:top-1/2 after:transform after:-translate-y-1/2 after:border-4 after:border-transparent after:border-r-neutral-900',
}

export interface TooltipProps {
  content: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right'
  showArrow?: boolean
  delay?: number
  disabled?: boolean
  className?: string
  children: ReactElement
}

export function Tooltip({
  content,
  position = 'top',
  showArrow = true,
  delay = 500,
  disabled = false,
  className,
  children,
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>()
  const tooltipId = useRef(`tooltip-${Math.random().toString(36).substr(2, 9)}`)

  const showTooltip = () => {
    if (disabled) return
    
    clearTimeout(timeoutRef.current)
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
    }, delay)
  }

  const hideTooltip = () => {
    clearTimeout(timeoutRef.current)
    setIsVisible(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      hideTooltip()
    }
  }

  // Animation variants
  const animationVariants = {
    initial: {
      opacity: 0,
      scale: 0.8,
      y: position === 'top' ? 10 : position === 'bottom' ? -10 : 0,
      x: position === 'left' ? 10 : position === 'right' ? -10 : 0,
    },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      x: 0,
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: position === 'top' ? 10 : position === 'bottom' ? -10 : 0,
      x: position === 'left' ? 10 : position === 'right' ? -10 : 0,
    },
  }

  const arrowClass = showArrow ? {
    top: tooltipVariants.arrowTop,
    bottom: tooltipVariants.arrowBottom,
    left: tooltipVariants.arrowLeft,
    right: tooltipVariants.arrowRight,
  }[position] : ''

  // Clone the child element to add event handlers
  const triggerElement = cloneElement(children, {
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip()
      children.props.onMouseEnter?.(e)
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip()
      children.props.onMouseLeave?.(e)
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip()
      children.props.onFocus?.(e)
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip()
      children.props.onBlur?.(e)
    },
    onKeyDown: (e: React.KeyboardEvent) => {
      handleKeyDown(e)
      children.props.onKeyDown?.(e)
    },
    'aria-describedby': isVisible ? tooltipId.current : undefined,
  })

  return (
    <div className="relative inline-block">
      {triggerElement}
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            id={tooltipId.current}
            role="tooltip"
            variants={animationVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
              duration: 0.2,
            }}
            className={cn(
              'absolute z-50 px-3 py-2 text-sm font-medium text-white bg-neutral-900 rounded-lg shadow-lg whitespace-nowrap pointer-events-none',
              tooltipVariants[position],
              arrowClass,
              className
            )}
          >
            {content}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

Tooltip.displayName = 'Tooltip'
