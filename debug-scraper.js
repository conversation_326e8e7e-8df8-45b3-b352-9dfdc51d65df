// Script de diagnostic pour identifier les problèmes du scraper
const http = require('http');
const https = require('https');

// Configuration des sites à tester
const SITES_CONFIG = {
  leboncoin: {
    name: '<PERSON><PERSON>onCoi<PERSON>',
    baseUrl: 'https://www.leboncoin.fr',
    searchUrls: [
      'https://www.leboncoin.fr/recherche?category=9&locations=Lyon_69000__45.764043_4.835659_15000&price=800000-1400000&text=entrepot',
      'https://www.leboncoin.fr/recherche?category=9&locations=Bordeaux_33000__44.837789_-0.57918_15000&price=800000-1400000&text=entrepot'
    ]
  },
  seloger: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    baseUrl: 'https://www.seloger.com',
    searchUrls: [
      'https://www.seloger.com/list.htm?types=11&places=[{%22inseeCodes%22:[69123]}]&price=800000/1400000',
      'https://www.seloger.com/list.htm?types=11&places=[{%22inseeCodes%22:[33063]}]&price=800000/1400000'
    ]
  },
  geolocaux: {
    name: 'Geolocaux',
    baseUrl: 'https://www.geolocaux.com',
    searchUrls: [
      'https://www.geolocaux.com/vente/entrepot/lyon/?prix_min=800000&prix_max=1400000&surface_min=400',
      'https://www.geolocaux.com/vente/entrepot/bordeaux/?prix_min=800000&prix_max=1400000&surface_min=400'
    ]
  }
};

// Fonction pour faire des requêtes HTTP avec headers réalistes
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;

    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'no-cache'
      }
    };

    const req = client.get(url, options, (res) => {
      let data = '';

      // Gérer la compression gzip
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib');
        const gunzip = zlib.createGunzip();
        res.pipe(gunzip);
        gunzip.on('data', chunk => data += chunk);
        gunzip.on('end', () => resolve(data));
        gunzip.on('error', reject);
      } else {
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      }
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
}

// Fonction de diagnostic pour un site
async function diagnoseSite(siteName, siteConfig) {
  console.log(`\n🔍 === DIAGNOSTIC ${siteConfig.name} ===`);
  
  for (let i = 0; i < siteConfig.searchUrls.length; i++) {
    const searchUrl = siteConfig.searchUrls[i];
    const city = searchUrl.includes('lyon') || searchUrl.includes('69') ? 'Lyon' : 'Bordeaux';
    
    console.log(`\n📍 Test ${city}:`);
    console.log(`🔗 URL: ${searchUrl}`);
    
    try {
      const startTime = Date.now();
      const html = await fetchPage(searchUrl);
      const loadTime = Date.now() - startTime;
      
      console.log(`✅ Réponse reçue en ${loadTime}ms`);
      console.log(`📄 Taille: ${html.length} caractères`);
      
      // Vérifier le statut de la page
      if (html.includes('404') || html.includes('Page non trouvée')) {
        console.log(`❌ Page 404 détectée`);
        continue;
      }
      
      if (html.includes('robot') || html.includes('captcha')) {
        console.log(`🤖 Protection anti-bot détectée`);
        continue;
      }
      
      // Analyser le contenu
      console.log(`\n📊 Analyse du contenu:`);
      
      // Chercher des mots-clés d'annonces
      const propertyKeywords = ['annonce', 'vente', 'immobilier', 'entrepot', 'entrepôt', 'local', 'bien'];
      const foundKeywords = propertyKeywords.filter(keyword => 
        html.toLowerCase().includes(keyword)
      );
      console.log(`🏷️ Mots-clés trouvés: ${foundKeywords.join(', ')}`);
      
      // Chercher des prix
      const priceMatches = html.match(/(\d+(?:\s?\d+)*)\s*€/g);
      if (priceMatches) {
        console.log(`💰 Prix détectés: ${priceMatches.slice(0, 5).join(', ')}${priceMatches.length > 5 ? '...' : ''}`);
      } else {
        console.log(`💰 Aucun prix détecté`);
      }
      
      // Chercher des superficies
      const areaMatches = html.match(/(\d+(?:[,.]?\d+)?)\s*m[²2]/gi);
      if (areaMatches) {
        console.log(`📐 Superficies détectées: ${areaMatches.slice(0, 5).join(', ')}${areaMatches.length > 5 ? '...' : ''}`);
      } else {
        console.log(`📐 Aucune superficie détectée`);
      }
      
      // Chercher des liens d'annonces
      const linkPatterns = [
        /href="([^"]*\/annonce[^"]*)"/gi,
        /href="([^"]*\/vente[^"]*)"/gi,
        /href="([^"]*\/detail[^"]*)"/gi,
        /href="([^"]*\/bien[^"]*)"/gi,
        /href="([^"]*\/immobilier[^"]*)"/gi
      ];
      
      let totalLinks = 0;
      for (const pattern of linkPatterns) {
        const matches = html.match(pattern);
        if (matches) {
          totalLinks += matches.length;
        }
      }
      console.log(`🔗 Liens d'annonces potentiels: ${totalLinks}`);
      
      // Sauvegarder un échantillon pour inspection manuelle
      const sample = html.substring(0, 2000);
      console.log(`\n📝 Échantillon HTML (premiers 2000 caractères):`);
      console.log(`${sample}...`);
      
    } catch (error) {
      console.log(`❌ Erreur: ${error.message}`);
    }
  }
}

// Fonction principale de diagnostic
async function runDiagnosis() {
  console.log('🚀 === DIAGNOSTIC SCRAPER INDUSTRIEL ===\n');
  
  const sites = Object.keys(SITES_CONFIG);
  
  for (const siteName of sites) {
    const siteConfig = SITES_CONFIG[siteName];
    await diagnoseSite(siteName, siteConfig);
    
    // Pause entre les sites
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n✅ === DIAGNOSTIC TERMINÉ ===');
  console.log('\n💡 Recommandations:');
  console.log('1. Vérifiez les URLs de recherche dans un navigateur');
  console.log('2. Adaptez les sélecteurs CSS selon les structures HTML trouvées');
  console.log('3. Testez avec des critères de recherche plus larges');
  console.log('4. Considérez l\'utilisation de Puppeteer pour les sites avec JavaScript');
}

// Démarrer le diagnostic
runDiagnosis().catch(console.error);
