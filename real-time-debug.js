// OUTIL DE DEBUG EN TEMPS RÉEL - Compare ce que vous voyez vs ce que le scraper récupère
const http = require('http');
const https = require('https');
const fs = require('fs');

const PORT = 3003;

let debugResults = {
  url: '',
  htmlContent: '',
  extractedData: {},
  analysis: {},
  timestamp: null
};

// Fonction pour récupérer une page exactement comme le scraper
function fetchPageDebug(url) {
  return new Promise((resolve, reject) => {
    console.log(`🔍 Récupération de: ${url}`);
    
    const client = url.startsWith('https') ? https : http;
    
    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      timeout: 20000
    };

    const req = client.get(url, options, (res) => {
      let data = '';
      
      console.log(`📡 Status: ${res.statusCode}`);
      console.log(`📋 Headers: ${JSON.stringify(res.headers, null, 2)}`);

      // Gérer la compression gzip
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib');
        const gunzip = zlib.createGunzip();
        res.pipe(gunzip);
        gunzip.on('data', chunk => data += chunk);
        gunzip.on('end', () => {
          console.log(`✅ Page récupérée: ${data.length} caractères`);
          resolve(data);
        });
        gunzip.on('error', reject);
      } else {
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          console.log(`✅ Page récupérée: ${data.length} caractères`);
          resolve(data);
        });
      }
    });

    req.on('error', (error) => {
      console.log(`❌ Erreur: ${error.message}`);
      reject(error);
    });
    
    req.setTimeout(20000, () => {
      req.destroy();
      reject(new Error('Timeout après 20 secondes'));
    });
  });
}

// Analyse complète du contenu HTML
function analyzeHTML(html, url) {
  const analysis = {
    totalLength: html.length,
    hasContent: html.length > 1000,
    is404: html.toLowerCase().includes('404') || html.toLowerCase().includes('page non trouvée'),
    hasAntiBot: html.toLowerCase().includes('robot') || html.toLowerCase().includes('captcha') || html.toLowerCase().includes('blocked'),
    hasRedirect: html.includes('refresh') || html.includes('location.href'),
    prices: [],
    areas: [],
    titles: [],
    links: [],
    keywords: []
  };

  // Extraire tous les prix
  const pricePatterns = [
    /(\d{3,}(?:\s?\d{3})*)\s*€/g,
    /(\d{3,}(?:\s?\d{3})*)\s*euros?/gi,
    /€\s*(\d{3,}(?:\s?\d{3})*)/g,
    /prix[^0-9]*(\d{3,}(?:\s?\d{3})*)/gi
  ];

  for (const pattern of pricePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const priceStr = match[1].replace(/\s/g, '');
      const price = parseInt(priceStr);
      if (price >= 50000 && price <= 10000000) {
        analysis.prices.push(price);
      }
    }
  }

  // Extraire toutes les superficies
  const areaPatterns = [
    /(\d+(?:[,.]?\d+)?)\s*m[²2]/gi,
    /(\d+(?:[,.]?\d+)?)\s*m²/gi,
    /surface[^0-9]*(\d+(?:[,.]?\d+)?)/gi
  ];

  for (const pattern of areaPatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const areaStr = match[1].replace(',', '.');
      const area = parseFloat(areaStr);
      if (area >= 10 && area <= 50000) {
        analysis.areas.push(area);
      }
    }
  }

  // Extraire les titres
  const titlePatterns = [
    /<title[^>]*>([^<]+)<\/title>/gi,
    /<h[1-6][^>]*>([^<]+)<\/h[1-6]>/gi
  ];

  for (const pattern of titlePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      const title = match[1].trim();
      if (title.length > 5 && title.length < 200) {
        analysis.titles.push(title);
      }
    }
  }

  // Extraire les liens d'annonces
  const linkPatterns = [
    /href="([^"]*(?:annonce|vente|detail|bien|immobilier|local|entrepot)[^"]*)"/gi
  ];

  for (const pattern of linkPatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      analysis.links.push(match[1]);
    }
  }

  // Chercher des mots-clés immobiliers
  const keywords = ['annonce', 'vente', 'immobilier', 'entrepot', 'entrepôt', 'local', 'industriel', 'hangar', 'warehouse'];
  for (const keyword of keywords) {
    const regex = new RegExp(keyword, 'gi');
    const matches = html.match(regex);
    if (matches) {
      analysis.keywords.push({ keyword, count: matches.length });
    }
  }

  // Déduplication
  analysis.prices = [...new Set(analysis.prices)].sort((a, b) => a - b);
  analysis.areas = [...new Set(analysis.areas)].sort((a, b) => a - b);
  analysis.titles = [...new Set(analysis.titles)];
  analysis.links = [...new Set(analysis.links)];

  return analysis;
}

// Interface web de debug
function generateDebugHTML() {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Scraper - Comparaison Temps Réel</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; color: #334155; line-height: 1.6; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; padding: 30px; text-align: center; margin-bottom: 30px; border-radius: 12px; }
        .test-section { background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px; }
        .input-group { margin-bottom: 20px; }
        .input-group label { display: block; margin-bottom: 5px; font-weight: 500; }
        .input-group input { width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 16px; }
        .btn { padding: 12px 24px; border: none; border-radius: 6px; font-weight: 500; cursor: pointer; transition: all 0.2s; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }
        .results-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; }
        .result-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result-title { font-size: 1.2rem; font-weight: 600; margin-bottom: 15px; color: #1e293b; }
        .data-list { max-height: 200px; overflow-y: auto; }
        .data-item { padding: 8px; background: #f8fafc; margin-bottom: 5px; border-radius: 4px; font-family: monospace; font-size: 0.9rem; }
        .status-good { color: #059669; }
        .status-bad { color: #dc2626; }
        .status-warning { color: #d97706; }
        .html-preview { background: #1e293b; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 0.8rem; max-height: 300px; overflow-y: auto; white-space: pre-wrap; }
        .comparison { background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Debug Scraper - Comparaison Temps Réel</h1>
            <p>Testez exactement ce que le scraper voit vs ce que vous voyez</p>
        </div>

        <div class="test-section">
            <h3>🎯 Test d'une URL spécifique</h3>
            <p>Collez l'URL exacte où vous voyez des annonces d'entrepôts :</p>
            
            <div class="input-group">
                <label for="test-url">URL à tester :</label>
                <input type="url" id="test-url" placeholder="https://www.leboncoin.fr/recherche?category=9&text=entrepot&locations=..." />
            </div>
            
            <button class="btn btn-danger" onclick="testURL()">🔍 Analyser cette URL</button>
        </div>

        ${debugResults.url ? `
        <div class="results-grid">
            <div class="result-card">
                <div class="result-title">📊 Analyse générale</div>
                <div class="data-item">URL testée: ${debugResults.url}</div>
                <div class="data-item">Taille HTML: ${debugResults.analysis.totalLength?.toLocaleString()} caractères</div>
                <div class="data-item ${debugResults.analysis.hasContent ? 'status-good' : 'status-bad'}">
                    Contenu suffisant: ${debugResults.analysis.hasContent ? '✅ Oui' : '❌ Non'}
                </div>
                <div class="data-item ${debugResults.analysis.is404 ? 'status-bad' : 'status-good'}">
                    Page 404: ${debugResults.analysis.is404 ? '❌ Oui' : '✅ Non'}
                </div>
                <div class="data-item ${debugResults.analysis.hasAntiBot ? 'status-bad' : 'status-good'}">
                    Anti-bot: ${debugResults.analysis.hasAntiBot ? '❌ Détecté' : '✅ Non'}
                </div>
                <div class="data-item ${debugResults.analysis.hasRedirect ? 'status-warning' : 'status-good'}">
                    Redirection: ${debugResults.analysis.hasRedirect ? '⚠️ Oui' : '✅ Non'}
                </div>
            </div>

            <div class="result-card">
                <div class="result-title">💰 Prix trouvés (${debugResults.analysis.prices?.length || 0})</div>
                <div class="data-list">
                    ${debugResults.analysis.prices?.slice(0, 20).map(price => 
                        `<div class="data-item">${price.toLocaleString()}€</div>`
                    ).join('') || '<div class="data-item status-bad">Aucun prix trouvé</div>'}
                </div>
            </div>

            <div class="result-card">
                <div class="result-title">📐 Superficies trouvées (${debugResults.analysis.areas?.length || 0})</div>
                <div class="data-list">
                    ${debugResults.analysis.areas?.slice(0, 20).map(area => 
                        `<div class="data-item">${area}m²</div>`
                    ).join('') || '<div class="data-item status-bad">Aucune superficie trouvée</div>'}
                </div>
            </div>

            <div class="result-card">
                <div class="result-title">🔗 Liens d'annonces (${debugResults.analysis.links?.length || 0})</div>
                <div class="data-list">
                    ${debugResults.analysis.links?.slice(0, 10).map(link => 
                        `<div class="data-item">${link}</div>`
                    ).join('') || '<div class="data-item status-bad">Aucun lien trouvé</div>'}
                </div>
            </div>

            <div class="result-card">
                <div class="result-title">🏷️ Mots-clés immobiliers</div>
                <div class="data-list">
                    ${debugResults.analysis.keywords?.map(kw => 
                        `<div class="data-item">${kw.keyword}: ${kw.count} occurrences</div>`
                    ).join('') || '<div class="data-item status-bad">Aucun mot-clé trouvé</div>'}
                </div>
            </div>

            <div class="result-card">
                <div class="result-title">📝 Titres trouvés (${debugResults.analysis.titles?.length || 0})</div>
                <div class="data-list">
                    ${debugResults.analysis.titles?.slice(0, 10).map(title => 
                        `<div class="data-item">${title}</div>`
                    ).join('') || '<div class="data-item status-bad">Aucun titre trouvé</div>'}
                </div>
            </div>
        </div>

        <div class="comparison">
            <h3>🎯 Diagnostic</h3>
            ${debugResults.analysis.prices?.length > 0 ? 
                '<p class="status-good">✅ Le scraper trouve des prix - c\'est bon signe !</p>' : 
                '<p class="status-bad">❌ Aucun prix trouvé - problème d\'extraction ou de contenu</p>'
            }
            ${debugResults.analysis.links?.length > 0 ? 
                '<p class="status-good">✅ Des liens d\'annonces sont présents</p>' : 
                '<p class="status-bad">❌ Aucun lien d\'annonce trouvé</p>'
            }
            ${debugResults.analysis.hasAntiBot ? 
                '<p class="status-bad">❌ Protection anti-bot détectée - il faut utiliser Puppeteer</p>' : 
                '<p class="status-good">✅ Pas de protection anti-bot</p>'
            }
        </div>

        <div class="test-section">
            <h3>📄 Aperçu HTML (premiers 2000 caractères)</h3>
            <div class="html-preview">${debugResults.htmlContent?.substring(0, 2000) || 'Aucun contenu'}</div>
        </div>
        ` : ''}
    </div>

    <script>
        async function testURL() {
            const url = document.getElementById('test-url').value;
            if (!url) {
                alert('Veuillez entrer une URL');
                return;
            }

            try {
                const response = await fetch('/api/debug-url', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    location.reload();
                } else {
                    alert('Erreur: ' + result.error);
                }
            } catch (error) {
                alert('Erreur: ' + error.message);
            }
        }
    </script>
</body>
</html>`;
}

// Serveur de debug
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(generateDebugHTML());
  } else if (url.pathname === '/api/debug-url' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', async () => {
      try {
        const { url: testUrl } = JSON.parse(body);
        
        console.log(`\n🔍 === TEST DEBUG URL ===`);
        console.log(`URL: ${testUrl}`);
        
        const html = await fetchPageDebug(testUrl);
        const analysis = analyzeHTML(html, testUrl);
        
        debugResults = {
          url: testUrl,
          htmlContent: html,
          analysis: analysis,
          timestamp: new Date()
        };
        
        console.log(`📊 Analyse terminée:`);
        console.log(`- Prix trouvés: ${analysis.prices.length}`);
        console.log(`- Superficies: ${analysis.areas.length}`);
        console.log(`- Liens: ${analysis.links.length}`);
        console.log(`- Mots-clés: ${analysis.keywords.length}`);
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true }));
        
      } catch (error) {
        console.error('❌ Erreur debug:', error);
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

server.listen(PORT, () => {
  console.log(`🔍 Outil de DEBUG démarré sur http://localhost:${PORT}`);
  console.log('🎯 Testez l\'URL exacte où vous voyez des annonces !');
});
