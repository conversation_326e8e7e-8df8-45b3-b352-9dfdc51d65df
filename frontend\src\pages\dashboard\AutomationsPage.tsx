import { useState } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { 
  Plus, 
  Search, 
  Filter, 
  Workflow, 
  Play, 
  Pause, 
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Clock,
  TrendingUp,
  Calendar
} from 'lucide-react'

export function AutomationsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const automations = [
    {
      id: 1,
      name: 'Traitement automatique des leads',
      description: 'Capture et qualification des leads depuis le site web',
      status: 'active',
      lastRun: '2 minutes ago',
      executions: 1247,
      successRate: 98.2,
      createdAt: '2024-01-15',
      tags: ['CRM', 'Marketing'],
      trigger: 'Webhook'
    },
    {
      id: 2,
      name: 'Synchronisation e-commerce',
      description: 'Sync des commandes et stocks entre plateformes',
      status: 'active',
      lastRun: '5 minutes ago',
      executions: 856,
      successRate: 99.1,
      createdAt: '2024-01-10',
      tags: ['E-commerce', 'Inventory'],
      trigger: 'Schedule'
    },
    {
      id: 3,
      name: 'Onboarding client automatisé',
      description: 'Séquence d\'accueil pour nouveaux clients',
      status: 'paused',
      lastRun: '1 hour ago',
      executions: 234,
      successRate: 97.8,
      createdAt: '2024-01-08',
      tags: ['Customer Success', 'Email'],
      trigger: 'Event'
    },
    {
      id: 4,
      name: 'Backup automatique des données',
      description: 'Sauvegarde quotidienne des données critiques',
      status: 'error',
      lastRun: '3 hours ago',
      executions: 12,
      successRate: 85.4,
      createdAt: '2024-01-05',
      tags: ['Backup', 'Security'],
      trigger: 'Schedule'
    },
    {
      id: 5,
      name: 'Notification Slack équipe vente',
      description: 'Alertes pour nouvelles opportunités',
      status: 'active',
      lastRun: '10 minutes ago',
      executions: 567,
      successRate: 100,
      createdAt: '2024-01-12',
      tags: ['Notifications', 'Sales'],
      trigger: 'Webhook'
    }
  ]

  const filteredAutomations = automations.filter(automation => {
    const matchesSearch = automation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         automation.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || automation.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'paused':
        return <Clock className="w-4 h-4 text-yellow-600" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 mb-2">
            Automatisations
          </h1>
          <p className="text-slate-600">
            Gérez et surveillez toutes vos automatisations
          </p>
        </div>
        <Link
          to="/dashboard/automations/new"
          className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105"
        >
          <Plus className="w-5 h-5 mr-2" />
          Nouvelle automatisation
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl border border-slate-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
            <input
              type="text"
              placeholder="Rechercher une automatisation..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="pl-10 pr-8 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none bg-white"
            >
              <option value="all">Tous les statuts</option>
              <option value="active">Actif</option>
              <option value="paused">En pause</option>
              <option value="error">Erreur</option>
            </select>
          </div>
        </div>
      </div>

      {/* Automations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredAutomations.map((automation, index) => (
          <motion.div
            key={automation.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-2xl border border-slate-200 p-6 hover:shadow-lg transition-all duration-300"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Workflow className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-slate-900">
                    {automation.name}
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    {getStatusIcon(automation.status)}
                    <span className={`text-xs font-medium px-2 py-1 rounded-full ${getStatusColor(automation.status)}`}>
                      {automation.status === 'active' ? 'Actif' : 
                       automation.status === 'paused' ? 'En pause' : 'Erreur'}
                    </span>
                  </div>
                </div>
              </div>
              <button className="text-slate-400 hover:text-slate-600">
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>

            {/* Description */}
            <p className="text-slate-600 text-sm mb-4">
              {automation.description}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {automation.tags.map((tag, tagIndex) => (
                <span
                  key={tagIndex}
                  className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-lg"
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-lg font-bold text-slate-900">
                  {automation.executions.toLocaleString()}
                </div>
                <div className="text-xs text-slate-500">Exécutions</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {automation.successRate}%
                </div>
                <div className="text-xs text-slate-500">Succès</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-slate-900">
                  {automation.trigger}
                </div>
                <div className="text-xs text-slate-500">Déclencheur</div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between pt-4 border-t border-slate-200">
              <div className="flex items-center space-x-2 text-xs text-slate-500">
                <Calendar className="w-4 h-4" />
                <span>Dernière exécution: {automation.lastRun}</span>
              </div>
              <div className="flex items-center space-x-2">
                {automation.status === 'active' ? (
                  <button className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors">
                    <Pause className="w-4 h-4" />
                  </button>
                ) : (
                  <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                    <Play className="w-4 h-4" />
                  </button>
                )}
                <Link
                  to={`/dashboard/automations/${automation.id}`}
                  className="p-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors"
                >
                  <TrendingUp className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {filteredAutomations.length === 0 && (
        <div className="text-center py-12">
          <Workflow className="w-16 h-16 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-slate-900 mb-2">
            Aucune automatisation trouvée
          </h3>
          <p className="text-slate-600 mb-6">
            {searchTerm || statusFilter !== 'all' 
              ? 'Essayez de modifier vos filtres de recherche.'
              : 'Commencez par créer votre première automatisation.'}
          </p>
          {!searchTerm && statusFilter === 'all' && (
            <Link
              to="/dashboard/automations/new"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
            >
              <Plus className="w-5 h-5 mr-2" />
              Créer une automatisation
            </Link>
          )}
        </div>
      )}
    </div>
  )
}
