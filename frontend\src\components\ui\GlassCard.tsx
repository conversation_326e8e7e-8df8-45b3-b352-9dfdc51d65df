import { forwardRef, HTMLAttributes } from 'react'
import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

export interface GlassCardProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  blur?: 'sm' | 'md' | 'lg' | 'xl'
  opacity?: 'low' | 'medium' | 'high'
  border?: boolean
  shadow?: 'none' | 'sm' | 'md' | 'lg'
  interactive?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

const blurVariants = {
  sm: 'backdrop-blur-sm',
  md: 'backdrop-blur-md',
  lg: 'backdrop-blur-lg',
  xl: 'backdrop-blur-xl',
}

const opacityVariants = {
  low: 'bg-white/10',
  medium: 'bg-white/20',
  high: 'bg-white/30',
}

const shadowVariants = {
  none: '',
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
}

const paddingVariants = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-12',
}

export const GlassCard = forwardRef<HTMLDivElement, GlassCardProps>(
  ({ 
    className,
    children,
    blur = 'md',
    opacity = 'medium',
    border = true,
    shadow = 'md',
    interactive = false,
    padding = 'md',
    ...props 
  }, ref) => {
    const Component = interactive ? motion.div : 'div'
    
    const baseClasses = cn(
      // Base styles
      'rounded-2xl',
      
      // Glass effect
      blurVariants[blur],
      opacityVariants[opacity],
      
      // Border
      border && 'border border-white/20',
      
      // Shadow
      shadowVariants[shadow],
      
      // Padding
      paddingVariants[padding],
      
      // Interactive styles
      interactive && 'transition-all duration-300 hover:bg-white/30 hover:shadow-lg cursor-pointer',
      
      className
    )

    const motionProps = interactive ? {
      whileHover: { y: -2, scale: 1.02 },
      transition: { type: "spring", stiffness: 300, damping: 30 }
    } : {}

    return (
      <Component
        ref={ref}
        className={baseClasses}
        {...(interactive ? motionProps : {})}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

GlassCard.displayName = 'GlassCard'

// Glass Button component
export interface GlassButtonProps extends HTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const buttonVariants = {
  primary: 'bg-white/20 hover:bg-white/30 text-white border-white/30',
  secondary: 'bg-black/10 hover:bg-black/20 text-neutral-800 border-black/20',
  ghost: 'bg-transparent hover:bg-white/10 text-white border-transparent',
}

const buttonSizes = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg',
}

export const GlassButton = forwardRef<HTMLButtonElement, GlassButtonProps>(
  ({ 
    className,
    children,
    variant = 'primary',
    size = 'md',
    disabled = false,
    leftIcon,
    rightIcon,
    ...props 
  }, ref) => {
    return (
      <motion.button
        ref={ref}
        className={cn(
          // Base styles
          'inline-flex items-center justify-center gap-2 rounded-xl font-medium',
          'backdrop-blur-md border transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          
          // Variants
          buttonVariants[variant],
          buttonSizes[size],
          
          className
        )}
        whileHover={!disabled ? { scale: 1.02 } : {}}
        whileTap={!disabled ? { scale: 0.98 } : {}}
        disabled={disabled}
        {...props}
      >
        {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
        {children}
        {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
      </motion.button>
    )
  }
)

GlassButton.displayName = 'GlassButton'
