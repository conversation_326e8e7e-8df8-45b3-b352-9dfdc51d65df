import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON>r, 
  Bell, 
  Shield, 
  CreditCard, 
  Key, 
  Globe, 
  Save,
  Eye,
  EyeOff,
  Plus,
  Trash2,
  Edit
} from 'lucide-react'

export function SettingsPage() {
  const [activeTab, setActiveTab] = useState('profile')
  const [showApiKey, setShowApi<PERSON>ey] = useState(false)
  const [notifications, setNotifications] = useState({
    email: true,
    slack: false,
    webhook: true,
    errors: true,
    success: false
  })

  const tabs = [
    { id: 'profile', name: 'Profil', icon: User },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Sécurité', icon: Shield },
    { id: 'billing', name: 'Facturation', icon: CreditCard },
    { id: 'api', name: 'API', icon: Key },
    { id: 'integrations', name: 'Intégrations', icon: Globe }
  ]

  const apiKeys = [
    { id: 1, name: 'Production API', key: 'ak_live_1234...', created: '2024-01-15', lastUsed: '2 min ago' },
    { id: 2, name: 'Development API', key: 'ak_test_5678...', created: '2024-01-10', lastUsed: '1 hour ago' }
  ]

  const integrations = [
    { name: 'Slack', status: 'connected', icon: '💬' },
    { name: 'Google Workspace', status: 'connected', icon: '📧' },
    { name: 'Shopify', status: 'disconnected', icon: '🛒' },
    { name: 'Salesforce', status: 'disconnected', icon: '☁️' }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Prénom
                </label>
                <input
                  type="text"
                  defaultValue="John"
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Nom
                </label>
                <input
                  type="text"
                  defaultValue="Doe"
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Email
              </label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Entreprise
              </label>
              <input
                type="text"
                defaultValue="Acme Corp"
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <button className="flex items-center px-6 py-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors">
              <Save className="w-5 h-5 mr-2" />
              Sauvegarder
            </button>
          </div>
        )

      case 'notifications':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              {Object.entries(notifications).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-4 bg-slate-50 rounded-xl">
                  <div>
                    <h3 className="font-medium text-slate-900 capitalize">
                      {key === 'webhook' ? 'Webhook' : key}
                    </h3>
                    <p className="text-sm text-slate-600">
                      {key === 'email' && 'Recevoir des notifications par email'}
                      {key === 'slack' && 'Notifications Slack'}
                      {key === 'webhook' && 'Notifications webhook'}
                      {key === 'errors' && 'Alertes d\'erreur'}
                      {key === 'success' && 'Notifications de succès'}
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setNotifications(prev => ({ ...prev, [key]: e.target.checked }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        )

      case 'api':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-slate-900">Clés API</h3>
              <button className="flex items-center px-4 py-2 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors">
                <Plus className="w-5 h-5 mr-2" />
                Nouvelle clé
              </button>
            </div>
            <div className="space-y-4">
              {apiKeys.map((apiKey) => (
                <div key={apiKey.id} className="p-4 border border-slate-200 rounded-xl">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-slate-900">{apiKey.name}</h4>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-slate-400 hover:text-slate-600 rounded-lg">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-red-400 hover:text-red-600 rounded-lg">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 mb-3">
                    <code className="flex-1 px-3 py-2 bg-slate-100 rounded-lg font-mono text-sm">
                      {showApiKey ? 'ak_live_1234567890abcdef' : apiKey.key}
                    </code>
                    <button
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="p-2 text-slate-400 hover:text-slate-600 rounded-lg"
                    >
                      {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                  <div className="flex items-center justify-between text-sm text-slate-500">
                    <span>Créé le {apiKey.created}</span>
                    <span>Dernière utilisation: {apiKey.lastUsed}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )

      case 'integrations':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {integrations.map((integration, index) => (
                <div key={index} className="p-6 border border-slate-200 rounded-xl">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{integration.icon}</span>
                      <h3 className="font-medium text-slate-900">{integration.name}</h3>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      integration.status === 'connected' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-slate-100 text-slate-600'
                    }`}>
                      {integration.status === 'connected' ? 'Connecté' : 'Déconnecté'}
                    </span>
                  </div>
                  <button className={`w-full py-2 px-4 rounded-xl font-medium transition-colors ${
                    integration.status === 'connected'
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'bg-purple-500 text-white hover:bg-purple-600'
                  }`}>
                    {integration.status === 'connected' ? 'Déconnecter' : 'Connecter'}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center py-12">
            <p className="text-slate-600">Contenu de l'onglet {activeTab}</p>
          </div>
        )
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-slate-900 mb-2">
          Paramètres
        </h1>
        <p className="text-slate-600">
          Gérez votre compte et vos préférences
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all ${
                  activeTab === tab.id
                    ? 'bg-purple-500 text-white'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl border border-slate-200 p-8"
          >
            <h2 className="text-2xl font-bold text-slate-900 mb-6">
              {tabs.find(tab => tab.id === activeTab)?.name}
            </h2>
            {renderTabContent()}
          </motion.div>
        </div>
      </div>
    </div>
  )
}
